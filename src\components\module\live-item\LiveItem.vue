<template>
  <div class="live-item" @click="handleClick()">
    <div class="live-cover">
      <!-- 直播封面 -->
      <img :src="liveData.liveCover" :alt="liveData.liveTitle" class="cover-image" />
      <!-- Live 标识 -->
      <div class="live-badge">
        <img class="live-dot" src="@/static/live/live-icon.gif" />Live
      </div>
      <!-- 播放按钮 -->
      <div class="play-button">
        <img src="@/static/live/play-icon.png" />
      </div>
    </div>

    <!-- 直播信息 -->
    <div class="live-info">
      <div class="streamer-info">
        <div class="info">
          <img :src="liveData.userImage" alt="收米直播" class="streamer-avatar" />
          <span class="streamer-name">{{ liveData.userName }}</span>
        </div>
        <!-- 观看人数 -->
        <div class="viewer-count">
          <img class="hot" src="@/static/live/<EMAIL>" />
          <!-- {{ formatViewerCount(liveData.hotMax) }} -->
          {{ liveData.hotNum }}
        </div>
      </div>
      <!-- v-else -->
      <!-- <p class="live-title" v-if="liveData.liveType === 'basket'">{{ liveData.liveTitle }} {{ liveData.awayName
      }} VS {{ liveData.homeName }}</p> -->
      <p class="live-title">{{ liveData.liveTitle }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import router from '@/router'

// interface Streamer {
//   name: string
//   avatar: string
// }

interface LiveData {
  matchId: number
  liveTitle: string
  liveCover: string
  userName: string
  userImage: string
  hotMax: number
  userId: number
  liveType: string
  url?: string
  sclassName: string
  homeName: string
  awayName: string
  hotNum: number
}

interface Props {
  liveData: LiveData
}

interface Emits {
  (e: 'click', liveData: LiveData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClick = () => {
  // emit('click', props.liveData)
  router.push({
    name: 'room',
    query: {
      matchId: props.liveData.matchId,
      userId: props.liveData.userId,
      liveTypeEnum: props.liveData.liveType
    }
  })
}

// const formatViewerCount = (count: number): string => {
//   if (count >= 10000) {
//     return `${(count / 10000).toFixed(1)}万`
//   } else if (count >= 1000) {
//     return `${(count / 1000).toFixed(1)}k`
//   }
//   return count.toString()
// }
</script>

<style lang="scss" scoped>
.live-item {
  width: 285px;
  height: 250px;
  border-radius: 8px;
  cursor: pointer;
  background-color: #fff;
  overflow: hidden;

  &:hover {
    .cover-image {
      transform: scale(1.1);
    }
  }

  .live-cover {
    position: relative;
    width: 100%;
    height: 168px;
    overflow: hidden;
    background: #f0f0f0;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.2s ease;
    }

    .live-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(251, 43, 31, 0.9);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;

      .live-dot {
        width: 16px;
        height: 10px;
        display: block;
      }
    }

    .play-button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(2);
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: all 0.2s ease;
    }



    &:hover .play-button {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  .live-info {
    padding: 12px 10px;

    .streamer-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      margin-bottom: 8px;

      .info {
        display: flex;
        align-items: center;
      }

      .streamer-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 4px;
      }

      .streamer-name {
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 160px;
      }
    }

    .live-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin: 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .viewer-count {
      color: #333;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;

      .hot {
        display: block;
        width: 12px;
        height: 12px;
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}
</style>