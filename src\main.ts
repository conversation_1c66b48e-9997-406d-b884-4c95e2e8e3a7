import { createApp } from 'vue'
// import { createPinia } from 'pinia'
import { usePinia } from '@/stores'
import { setHandle401Callback } from '@/request/request'

import App from './App.vue'
import router from '@/router/inspect'
import './assets/base.css'

const app = createApp(App)
// app.use(createPinia())
usePinia(app)
app.use(router)

// 设置401错误处理回调
setHandle401Callback(() => {
  // 动态导入stores，避免在模块加载时使用
  import('@/stores/user').then(({ useUserStore }) => {
    import('@/stores/auth').then(({ useAuthStore }) => {
      const userStore = useUserStore()
      const authStore = useAuthStore()

      userStore.clearUserData()
      // router.replace('/')
      authStore.showLogin()
    })
  })
})

app.mount('#app')
