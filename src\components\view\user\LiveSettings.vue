<template>
  <div class="live-settings">
    <div class="settings-header">
      <div class="line"></div>
      <h3>直播设置</h3>
    </div>
    <n-form class="live-settings-form" ref="liveSettingsFormRef" :model="liveSettingsFormValue" label-placement="left"
      require-mark-placement="left" label-width="auto" :rules="liveSettingsRules">
      <!-- 关联比赛 -->
      <n-form-item label="关联比赛" path="matchId">
        <n-select v-model:value="liveSettingsFormValue.matchId" placeholder="请选择" :options="matchOptions"
          :render-label="renderOption" :menu-props="{ style: { maxHeight: '300px', overflowY: 'auto' } }"
          @update:value="handleSelect" :disabled="isLive" />
      </n-form-item>
      <!-- 直播封面 -->
      <n-form-item label="直播封面" path="liveCover">
        <div>
          <n-upload :loading="isUploading" v-model:file-list="fileList" list-type="image-card" :max="1" accept="image/*"
            @before-upload="handleBeforeUpload" @remove="handleRemoveImage" @finish="handleCoverUploadFinish" />
          <div class="upload-tip">
            请上传大小不超过<span>5MB</span>格式为<span>png/jpg/jpeg</span>的文件
          </div>
        </div>
      </n-form-item>
      <!-- 直播标题 -->
      <n-form-item label="直播标题" path="liveTitle">
        <n-input v-model:value="liveSettingsFormValue.liveTitle" placeholder="请输入直播标题" maxlength="30" />
        <button v-if="isLive" class="right-btn" @click="handleUpdateLiveTitle()">保存</button>
      </n-form-item>
      <!-- 公告 -->
      <n-form-item label="房间公告" path="liveNotice">
        <n-input v-model:value="liveSettingsFormValue.liveNotice" placeholder="请输入直播间公告" maxlength="200" />
        <button v-if="isLive" class="right-btn" @click="handleUpdateLiveNotice()">保存</button>
      </n-form-item>
      <!-- 推流码 -->
      <n-form-item label="推流码">
        <n-input v-model:value="liveSettingsFormValue.pushUrl" placeholder="请输入推流码" maxlength="100" />
        <button class="right-btn" @click="handleCopy(liveSettingsFormValue.pushUrl)">复制</button>
      </n-form-item>
      <!-- 开始、结束直播按钮 -->
      <div class="start-live-box">
        <button class="start-live-btn" @click="handleStartLive(isLive)">
          {{ isLive ? '结束直播' : '开始直播' }}
        </button>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { useMessage, type FormInst, type UploadFileInfo } from 'naive-ui'
import { uploadFilePathFunH5 } from '@/common/upload'
import { getLastLiveDetailApi, getLiveDetailApi, getReserveSuccessPageApi, startLiveApi, endLiveApi, updateLiveApi } from '@/api/user'

const message = useMessage()
const liveSettingsFormRef = ref<FormInst | null>(null)
const fileList = ref<UploadFileInfo[]>([])

// 响应式数据
const liveSettingsFormValue = reactive({
  roomId: null as number | null,
  matchId: null as number | null,
  liveCover: '',
  liveTitle: '',
  liveNotice: '',
  pushUrl: ''
})

// 表单校验
const liveSettingsRules = {
  matchId: [{
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择关联比赛',
    validator: (rule: any, value: string | null | undefined) => {
      return value !== null && value !== undefined && value !== ''
    }
  }],
  liveCover: [{
    required: true,
    trigger: ['blur', 'change'],
    message: '请上传直播封面',
    validator: (rule: any, value: string) => {
      return !!value || fileList.value.length > 0
    }
  }],
  liveTitle: [{
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入直播标题'
  }]
  ,
  liveNotice: [{
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入直播间公告'
  }]
}

const matchOptions = ref<any>([])
// 获取预约成功比赛列表
const getReserveSuccessList = async () => {
  const { data } = await getReserveSuccessPageApi({})
  data.records.map(((item: {
    liveTitle: string; matchId: number; id: number; liveType: string; matchBaseInfoRes: {
      sclassName: string; matchTime: string;
      homeName: string; homeLogo: string; awayName: string; awayLogo: string
    }
  }) => {
    if (item.matchBaseInfoRes) {
      const { sclassName, matchTime, homeName, homeLogo, awayName, awayLogo } = item.matchBaseInfoRes
      matchOptions.value.push({
        id: item.id,
        label: item.liveTitle,
        value: item.matchId,
        liveType: item.liveType,
        sclassName,
        matchTime,
        homeName,
        homeLogo,
        awayName,
        awayLogo
      })
    }

  }))
}

// 自定义选项渲染函数
const renderOption = ({ sclassName, matchTime, homeName, homeLogo, awayName, awayLogo, liveType }) => {
  if (liveType !== 'basket') {
    return h('div', {
      style: 'display: flex'
    }, [
      h('div', {
        style: 'max-width: 200px; margin-right: 20px;'
      }, sclassName),
      h('div', {
        style: 'margin-right: 20px;'
      }, dayjs(matchTime).format('YYYY-MM-DD HH:mm:ss')),
      h('div', {}, homeName),
      h('img', {
        src: homeLogo,
        style: 'width: 20px; height: 20px; object-fit: contain; margin-left: 5px;'
      }),
      h('div', {
        style: 'margin: 0 8px;'
      }, 'VS'),
      h('div', {}, awayName),
      h('img', {
        src: awayLogo,
        style: 'width: 20px; height: 20px; object-fit: contain; margin-left: 5px;'
      })
    ])
  } else {
    return h('div', {
      style: 'display: flex'
    }, [
      h('div', {
        style: 'max-width: 200px; margin-right: 20px;'
      }, sclassName),
      h('div', {
        style: 'margin-right: 20px;'
      }, dayjs(matchTime).format('YYYY-MM-DD HH:mm:ss')),
      h('div', {}, awayName),
      h('img', {
        src: awayLogo,
        style: 'width: 20px; height: 20px; object-fit: contain; margin-left: 5px;'
      }),
      h('div', {
        style: 'margin: 0 8px;'
      }, 'VS'),
      h('div', {}, homeName),
      h('img', {
        src: homeLogo,
        style: 'width: 20px; height: 20px; object-fit: contain; margin-left: 5px;'
      })
    ])
  }

}

// 上传直播封面
const isUploading = ref(false)
const handleBeforeUpload = async (options: { file: { file: string | Blob } }) => {
  if (isUploading.value) {
    message.warning('请等待当前图片上传完成')
    return false
  }
  try {
    isUploading.value = true;
    const formData = new FormData()
    formData.append('file', options.file.file)
    const { data: result } = await uploadFilePathFunH5(formData)
    if (!isLive.value) { message.success('上传直播封面成功') }
    liveSettingsFormValue.liveCover = result
    // 如果是直播中，立即更新封面
    if (isLive.value) {
      await updateLiveApi({
        id: liveSettingsFormValue.roomId,
        liveCover: result
      })
      message.success('修改直播封面成功')
    }
  } catch (error) {

  } finally {
    isUploading.value = false
  }

}

// 删除直播封面
const handleRemoveImage = () => {
  fileList.value = [] // 清空已上传的文件列表
  liveSettingsFormValue.liveCover = '' // 重置封面 URL
}

// 上传直播封面结束的回调
const handleCoverUploadFinish = async () => {
  if (!isLive.value) return
  try {
    await updateLiveApi({
      id: liveSettingsFormValue.roomId,
      liveCover: liveSettingsFormValue.liveCover
    })
  } catch (error) {
    message.error('修改直播封面失败')
    // 回滚封面
    if (fileList.value.length > 0 && fileList.value[0].url) {
      liveSettingsFormValue.liveCover = fileList.value[0].url as string
    }
  }
}

// 获取上场直播信息
const getLastLiveDetail = async () => {
  const { data } = await getLastLiveDetailApi({})
  if (!data) return
  const { liveCover, liveNotice, liveState } = data
  fileList.value = [{
    url: liveCover,
    id: '',
    name: '',
    status: 'finished'
  }]
  liveSettingsFormValue.liveCover = liveCover
  liveSettingsFormValue.liveNotice = liveNotice
  // 开播状态 0:未开播 1:开始 2：结束
  isLive.value = liveState === 1
}

const isLive = ref<boolean>(false)
// 获取正在直播信息
const getLiveDetail = async () => {
  const { data } = await getLiveDetailApi({})
  if (!data) {
    // 没有直播获取上一场的直播信息
    getLastLiveDetail()
  }
  else {
    // 获取正在直播信息
    const { id, matchId, liveCover, liveTitle, liveNotice, liveState } = data
    fileList.value = [{
      url: liveCover,
      id: '',
      name: '',
      status: 'finished'
    }]
    liveSettingsFormValue.roomId = id
    liveSettingsFormValue.matchId = matchId
    liveSettingsFormValue.liveCover = liveCover
    liveSettingsFormValue.liveTitle = liveTitle
    liveSettingsFormValue.liveNotice = liveNotice
    // 开播状态 0:未开播 1:开始 2：结束
    isLive.value = liveState === 1
  }
}

// 选择比赛
const handleSelect = (value: number) => {
  matchOptions.value.find((item: { value: number; id: number | null; label: string; liveType: string | null; homeName: string; awayName: string, sclassName: string }) => {
    if (item.value === value) {
      if (item.liveType !== 'basket') {
        liveSettingsFormValue.liveTitle = `${item.sclassName} ${item.label}`
      } else {
        liveSettingsFormValue.liveTitle = `${item.sclassName} ${item.awayName}'VS ${item.homeName}`
      }
      liveSettingsFormValue.roomId = item.id

    }
  })
}

// 修改直播标题
const handleUpdateLiveTitle = async () => {
  if (!isLive) return
  await updateLiveApi({
    id: liveSettingsFormValue.roomId,
    liveTitle: liveSettingsFormValue.liveTitle
  })
  message.success('修改直播标题成功')
  await getLiveDetail()
}

// 修改房间公告
const handleUpdateLiveNotice = async () => {
  if (!isLive) return
  await updateLiveApi({
    id: liveSettingsFormValue.roomId,
    liveNotice: liveSettingsFormValue.liveNotice
  })
  message.success('修改房间公告成功')
  await getLiveDetail()
}

// 复制推流码
const handleCopy = (value: string) => {
  if (value === '') {
    window.$message.warning('请输入推流码')
    return
  }
  navigator.clipboard.writeText(value).then(() => {
    window.$message.success('复制成功')
  })
}

// 开始、结束直播
const handleStartLive = async (isLive: boolean) => {
  if (isLive) {
    await endLiveApi({ roomId: liveSettingsFormValue.roomId })
    message.success('结束直播成功')
    liveSettingsFormValue.matchId = null
    liveSettingsFormValue.liveTitle = ''
    getLastLiveDetail()
  } else {
    liveSettingsFormRef.value?.validate(async (errors) => {
      if (!errors) {
        await startLiveApi(liveSettingsFormValue)
        message.success('开始直播成功')
        getLiveDetail()
      }
    })
  }
}

onMounted(async () => {
  await getReserveSuccessList()
  await getLiveDetail()
})
</script>

<style lang="scss" scoped>
.live-settings {
  background-color: #FFFFFF;
  min-height: 100%;

  .settings-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .live-settings-form {
    padding: 40px;

    .upload-tip {
      font-size: 12px;
      color: #999;
      line-height: 1.5;
      margin-top: 10px;

      span {
        color: #FB2B1F;
      }
    }

    .right-btn {
      width: 70px;
      height: 34px;
      background: #FB2B1F;
      border-radius: 36px;
      color: #FFFFFF;
      margin-left: 20px;
      border: none;
      cursor: pointer;
      transition: all .3s ease;
      font-size: 14px;

      &:hover {
        background: #E02419;
      }
    }

    .start-live-box {
      text-align: center;
      margin-top: 40px;

      .start-live-btn {
        width: 100%;
        max-width: 400px;
        padding: 12px 24px;
        background: #FB2B1F;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all .3s ease;

        &:hover {
          background: #E02419;
        }
      }
    }
  }
}
</style>
