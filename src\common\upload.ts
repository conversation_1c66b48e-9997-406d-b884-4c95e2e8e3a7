import Compressor from 'compressorjs'
import { signRequest } from '@/utils/sign'
import { useMessage } from 'naive-ui'

const baseUrl = import.meta.env.VITE_API_URL

export const uploadFileFunH5 = async function (data, ossEnum = '') {
  // 构建URL，添加ossEnum查询参数
  let url = `${baseUrl}/live-customer/upload/image`
  // if (ossEnum) {
  //   url += `?ossEnum=${ossEnum}`
  // }
  let config = {
    url,
    name: 'file',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }

  config = signRequest(config)
  try {
    const headers = new Headers()
    Object.keys(config.headers).forEach((key) => {
      if (key.toLowerCase() !== 'content-type') {
        headers.append(key, config.headers[key])
      }
    })

    const response = await fetch(config.url, {
      method: 'POST',
      headers,
      body: config.data
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('上传失败，响应:', errorText)
      return Promise.reject({
        errMsg: `statusCode is ${response.status}`,
        details: errorText
      })
    } else {
      const responseData = await response.json()
      console.log('上传成功，响应数据:', responseData)
      return responseData
    }
  } catch (e) {
    console.error('上传过程中发生错误:', e)
    return Promise.reject(e)
  }
}

export const uploadImageH5 = function (ossEnum = '') {
  const message = useMessage()
  return new Promise((resolve, reject) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.multiple = false
    input.onchange = async (e: any) => {
      const file = e.target?.files[0]
      if (!file) {
        return reject(new Error('请选择图片'))
      }

      new Compressor(file, {
        quality: 0.85,
        maxWidth: 200,
        maxHeight: 200,
        convertSize: 500 * 1024,
        async success(compressedFile) {
          try {
            const formData = new FormData()
            formData.append('file', compressedFile, file.name)
            const H5res = await uploadFileFunH5(formData, ossEnum)
            resolve(H5res)
          } catch (err) {
            message.error('上传失败')
            reject(err)
          }
        },
        error(err) {
          message.error('压缩失败')
          reject(err)
        }
      })
    }
    input.click()
  })
}

export const uploadFilePathFunH5 = async function (data) {
  let url = `${baseUrl}/live-customer/upload/image`
  let config = {
    url,
    name: 'file',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }
  // 应用签名
  config = signRequest(config)

  try {
    // 创建一个新的 Headers 对象，排除可能干扰 FormData 的头部
    const headers = new Headers()

    // 添加所有必要的头部，但排除 Content-Type
    Object.keys(config.headers).forEach((key) => {
      if (key.toLowerCase() !== 'content-type') {
        headers.append(key, config.headers[key])
      }
    })

    // 发送请求
    const response = await fetch(config.url, {
      method: 'POST',
      headers,
      body: config.data
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('上传失败，响应:', errorText)
      return Promise.reject({
        errMsg: `statusCode is ${response.status}`,
        details: errorText
      })
    } else {
      const responseData = await response.json()
      console.log('上传成功，响应数据:', responseData)
      return responseData
    }
  } catch (e) {
    console.error('上传过程中发生错误:', e)
    return Promise.reject(e)
  }
}
