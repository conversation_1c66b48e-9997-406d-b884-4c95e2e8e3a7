import request from '@/request/request'

interface RegisterType {
  phone: string
  // userName: string
  pwd: string
  smsCode: string
}
// c端用户手机号注册
export const registerApi = (data: RegisterType) => {
  return request.post('/live-customer/login/register.e', data)
}

interface LoginType {
  phone: string
  password: string
}
// c端用户密码登录
export const loginApi = (data: LoginType) => {
  return request.post('/live-customer/login/pwd.e', data)
}

interface SmsCodeType {
  phone: string
  businessType: string
}
// c端发送验证码
export const getSmsCodeApi = (params: SmsCodeType) => {
  return request.get('/live-customer/login/sms.e', { params })
}

// c端用户退出登录
export const loginOutApi = () => {
  return request.post('/live-customer/login/out')
}

// 用户基础信息查询
export const getBaseUserInfoApi = () => {
  return request.get('/live-customer/user/baseUserInfo')
}

interface headImgType {
  imgUrl: string
}
// 修改用户头像
export const updateHeadImgApi = (data: headImgType) => {
  return request.post('/live-customer/user/updateHeadImg', data)
}

interface userNameType {
  name: string
}
// 修改用户昵称
export const updateUserNameApi = (data: userNameType) => {
  return request.post('/live-customer/user/updateName', data)
}

interface userSexType {
  sex: string
}
// 修改用户性别
export const updateUserSexApi = (data: userSexType) => {
  return request.post('/live-customer/user/updateSex', data)
}

interface userBirthdayType {
  birthday: string
}
// 修改用户生日
export const updateUserBirthdayApi = (data: userBirthdayType) => {
  return request.post('/live-customer/user/updateBirthday', data)
}

interface userPhoneType {
  phone: string
  smsCode: string
  uuid: string
}
// 修改用户手机号
export const updateUserPhoneApi = (data: userPhoneType) => {
  return request.post('/live-customer/user/updatePhone', data)
}

interface userPasswordType {
  phone: string
  smsCode: string
  pwd: string
}
// 修改用户登录密码
export const updateUserPasswordApi = (data: userPasswordType) => {
  return request.post('/live-customer/user/updatePwd', data)
}

// 找回用户登录密码
export const retrieveUserPasswordApi = (data: userPasswordType) => {
  return request.post('/live-customer/user/retrievePwd.e', data)
}

interface checkCodeType {
  smsCode: string
  phone: string
  businessType: string
}
// 校验用户验证码 并生成10分钟可修改令牌
export const checkCodeApi = (data: checkCodeType) => {
  return request.post('/live-customer/checkCert', data)
}

export interface feedbackType {
  contentType: string
  content: string
  contactWay: string
  contentImages: string[]
  // type: number
  // targetId: number
}
// 用户提交意见反馈
export const feedbackApi = (data: feedbackType) => {
  return request.post('/live-customer/contact/submit', data)
}

interface followUserType {
  liveId: number
}
//关注主播
export const followUserApi = (data: followUserType) => {
  return request.post('/live-customer/userCollectLive/insert', data)
}
//取消关注主播
export const cancelFollowUserApi = (data: followUserType) => {
  return request.post('/live-customer/userCollectLive/cancel', data)
}

interface hotNumType {
  matchId: number
  userId: number
}
//增加主播热度
export const addHotNumApi = (data: hotNumType) => {
  return request.post('/live-customer/liveUserRoom/hotNum', data)
}

// 分页查询收藏主播列表
export const getCollectAnchorPageApi = (params: {}) => {
  return request.get('/live-customer/userCollectLive/page', { params })
}

interface CollectMatchType {
  current: number
  size: number
}
// 分页查询收藏比赛列表
export const getCollectMatchPageApi = (params: CollectMatchType) => {
  return request.get('/live-customer/userCollectMatch/page', { params })
}

interface MatchType {
  date: string
  liveTypeEnum: string
}
// 查询主播赛事列表
export const getMatchPageApi = (params: MatchType) => {
  return request.get('/live-customer/liveUserRoom/matchPage', { params })
}

// 我的预约成功 未开播直播
export const getReserveSuccessPageApi = (params: {}) => {
  return request.get('/live-customer/liveUserRoom/myPage', { params })
}

interface reserveMatchLiveType {
  matchId: number
  liveType: string
}
// 预约直播
export const reserveMatchLiveApi = (data: reserveMatchLiveType) => {
  return request.post('/live-customer/liveUserRoom/save', data)
}

// 查询上一场直播信息
export const getLastLiveDetailApi = (params: {}) => {
  return request.get('/live-customer/liveUserRoom/detail', { params })
}

// 查询正在直播信息
export const getLiveDetailApi = (params: {}) => {
  return request.get('/live-customer/liveUserRoom/processDetail', { params })
}

interface startLiveType {
  roomId: number | null
  matchId: number | null
  liveCover: string
  liveTitle: string
  liveNotice: string
  pushUrl: string
}
// 开始直播
export const startLiveApi = (data: startLiveType) => {
  return request.post('/live-customer/liveUserRoom/staLive', data)
}

interface endLiveType {
  roomId: number | null
}
// 结束直播
export const endLiveApi = (data: endLiveType) => {
  return request.post('/live-customer/liveUserRoom/endLive', data)
}

interface updateLiveType {
  id: number | null
  liveTitle?: string
  liveNotice?: string
  liveCover?: string
}
// 修改直播
export const updateLiveApi = (data: updateLiveType) => {
  return request.post('/live-customer/liveUserRoom/update', data)
}
//查询自己是否是管理员
interface getUserManageType {
  roomId: number | null
}
export const getUserManageApi = (params: getUserManageType) => {
  return request.get('/live-customer/liveRoomManage/getUserManage', { params })
}
//查询经验等级配置信息
export const getConfigLevelApi = () => {
  return request.get('/live-customer/configLevel/selectConfigLevel')
}
//查询用户每日经验任务完成情况
export const getConfigTodoListApi = () => {
  return request.get('/live-customer/configLevel/selectByTime')
}
//每日球票任务完成情况
export const getTaskTodoListApi = () => {
  return request.get('/live-customer/userGiveGift/selectByTime')
}
//用户赠送礼物
interface GiftTypeEnum {
  liveId: number
  roomId: number
  giftId: number
}
export const userGiveGiftApi = (data: GiftTypeEnum) => {
  return request.post('/live-customer/userGiveGift/insert', data)
}
/**
 * 任务类型
 */
enum TicketConfigEnum {
  DailyBarrage = "dailyBarrage",
  DailyFollow = "dailyFollow",
  FirstLogin = "firstLogin",
  InviteFriends = "InviteFriends",
  RewardAnchor = "rewardAnchor",
  SignInFive = "signInFive",
  SignInFour = "signInFour",
  SignInOne = "signInOne",
  SignInSeven = "signInSeven",
  SignInSix = "signInSix",
  SignInThree = "signInThree",
  SignInTwo = "signInTwo",
  ViewingDuration = "viewingDuration",
  signIn = "signIn",
}
interface TaskTodoListType {
  /**
   * 任务类型
   */
  ticketConfigEnum: TicketConfigEnum;
}
export const insertTicketApi = (data: TaskTodoListType) => {
  return request.post('/live-customer/userGiveGift/insertTicket', data)
}

//提交经验任务完成请求
enum ExpConfigEnum {
  DailyLogin = "dailyLogin",//每日登录
  ShareLiveRoom = "shareLiveRoom",//分享直播间
}
interface insertExpType {
  expConfigEnum: ExpConfigEnum
}
export const insertExpApi = (data: insertExpType) => {
  return request.post('/live-customer/configLevel/insert', data)
}

