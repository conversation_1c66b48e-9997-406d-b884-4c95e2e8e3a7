<template>
  <div class="chat-warp">
    <div class="chat-list">
      <div class="chat-item" v-for="(chat, index) in receivedMessages" :key="index">
        <!-- 进入直播间 -->
        <div class="enter-box" v-if="chat.type === 'ENTER'">
          <span class="label">Hi</span><span class="user-name">{{ chat.user.userName }}</span><span
            class="tip">进入直播间</span>
        </div>
        <!-- 文本通知 -->
        <div class="text-box" v-if="chat.type === 'TEXT'">
          <div class="identity" v-if="chat.user.id === props.roomId">主播</div>
          <div class="nationality" v-if="chat.user.tag">
            <img :src="chat.user.tag" referrerpolicy="no-referrer" class="icon">
            <!-- <img src="@/static/live/<EMAIL>" class="icon"> -->
          </div>
          <div class="level">
            <Level :level="chat.user.level ? parseInt(chat.user.level) : 1"></Level>
          </div>
          <div class="user-name" @click="handleOpenNormalCard(chat)">{{ chat.user.userName }}：
          </div>
          <div class="content">{{ chat.content }}</div>
        </div>
        <!-- 礼物 -->
        <div class="text-box" v-if="chat.type === 'GIFT'">
          <div class="identity" v-if="chat.user.id === props.roomId">主播</div>
          <div class="nationality" v-if="chat.user.tag">
            <img :src="chat.user.tag" referrerpolicy="no-referrer" class="icon">
            <!-- <img src="@/static/live/<EMAIL>" class="icon"> -->
          </div>
          <div class="level">
            <Level :level="chat.user.level ? parseInt(chat.user.level) : 1"></Level>
          </div>
          <div class="user-name" @click="handleOpenNormalCard(chat)">{{ chat.user.userName }}：
          </div>
          <div class="gift-content">
            送出{{ getGiftInfo(chat.content)?.name }}<img class="gift-icon" :src="getGiftInfo(chat.content)?.url" /> <span
              class="hit" v-if="chat.giftCount && chat.giftCount > 1"> x{{ chat.giftCount || 1 }}</span>
          </div>
        </div>
        <!-- 被挤掉线 -->
        <div class="reconnection-box" v-if="chat.type === 'RECONNECTION'">
          <img class="icon" src="@/static/live/<EMAIL>" /><span class="user-name">消息提醒：</span><span
            class="text">您的账号在另一个地点登录，弹幕连接已断开 <span class="tip" @click="onReconnection">点击重新连接</span></span>
        </div>
        <!-- 系统通知 -->
        <div class="notice-box" v-if="chat.type === 'NOTICE'">
          <img class="icon" src="@/static/live/<EMAIL>" /><span class="user-name">系统公告：</span><span
            class="tip" v-html="convertUrlToAnchor(chat.content)"></span>
        </div>
        <!-- 关注主播 -->
        <div class="text-box" v-if="chat.type === 'FOCUS'">
          <div class="nationality" v-if="chat.user.tag">
            <img :src="chat.user.tag" referrerpolicy="no-referrer" class="icon">
            <!-- <img src="@/static/live/<EMAIL>" class="icon"> -->
          </div>
          <div class="level">
            <Level :level="chat.user.level ? parseInt(chat.user.level) : 1"></Level>
          </div>
          <div class="user-name" @click="handleOpenNormalCard(chat)">{{ chat.user.userName }}
          </div>
          <div class="content" :class="{ 'focus': true }">{{ chat.content }}</div>
        </div>
      </div>
    </div>
    <div class="normal-card" ref="normalCardRef" v-show="isShowNoramlCard">
      <div class="sub-normal-card">
        <div class="user-info">
          <div class="other">
            <div class="identity" v-if="chatItem.user.id === props.roomId">主播</div>
            <div class="nationality" v-if="chatItem.user.tag">
              <img :src="chatItem.user.tag" referrerpolicy="no-referrer" class="icon">
            </div>
            <div class="level">
              <Level :level="chatItem.user.level ? parseInt(chatItem.user.level) : 1"></Level>
            </div>
          </div>
          <p class="name">{{ chatItem.user.userName }}</p>
        </div>
        <div class="btns-box" v-if="getIsManage(chatItem.user.id) || isHost(chatItem.user.id)">
          <div class="btn" v-if="isProhibit(chatItem.user.id)" @click="handleShutUp('lift')">取消禁言</div>
          <div class="btn" v-else @click="handleShutUp('ban')">禁言</div>
        </div>
        <div class="close" @click="isShowNoramlCard = false">×</div>
      </div>
    </div>
    <div class="chat-bottom">
      <div class="scroll-bottom" @click="scrollToBottom" v-if="isNeedScroll && isNewMessage">
        <img class="icon" src="@/static/live/<EMAIL>" /> 底部有新消息
      </div>
      <div class="operate-box">
        <n-popover trigger="hover">
          <template #trigger>
            <div class="shield">
              <img class="icon" src="@/static/live/<EMAIL>" />
            </div>
          </template>
          <n-checkbox v-model:checked="isShowEnter">
            屏蔽进场欢迎
          </n-checkbox>
        </n-popover>
        <div class="shield" @click="isShowGift = true">
          <img class="icon" src="@/static/live/<EMAIL>" />
        </div>
        <!-- <n-popover trigger="click">
          <template #trigger>
            
          </template>
          <div class="qr-code-popup">
            <div class="qr-placeholder"><img class="icon" src="@/static/live/<EMAIL>" /></div>
            <div class="label" style="text-align: center; color: #818181;">
              <p>用手机浏览器扫一扫</p>
              <p>精彩马上呈现</p>
            </div>
          </div>
        </n-popover> -->
      </div>
      <div class="chat-input-warp">
        <div class="input-box">
          <div class="textarea-box" v-if="isLogin">
            <textarea v-model="sendContent" :maxLength="40" class="chat-textarea" placeholder="快来唠两句呗~"
              @keydown="handleKeyDown"></textarea>
          </div>
          <div class="not-login" v-else @click="checkLogin">
            <p><span class="label">登录</span>后才可发送消息哦~~</p>
          </div>
        </div>
        <button class="send-btn" :class="{ disabled: !sendContent.trim() }" @click="throttledSendMessage">发送</button>
      </div>
    </div>
    <div class="gift-animation-warp">
      <transition-group name="gift-slide" tag="div" class="gift-list">
        <div class="gift-item" v-for="(item, index) in giftShowList" :key="`${item.userId}-${item.url}`"
          :class="{ 'gift-combo': item.giftCount > 1 }">
          <div class="gift-content">
            <div>
              <p class="username">{{ item.username }}</p>
              <p class="action">送出{{ item.giftName }}</p>
            </div>
            <img class="gift-icon" :src="item.url" />
            <canvas id="gift-canvas" ref="giftCanvas"></canvas>
            <!-- v-if="item.giftCount > 1" -->
            <img class="x" src="@/static/live/<EMAIL>" /> <span class="count" :key="item.giftCount">{{ item.giftCount
            }}</span>
          </div>
          <div class="gift-bg"></div>
        </div>
      </transition-group>

    </div>
    <div class="gift-warp" v-show="isShowGift">
      <div class="close" @click="isShowGift = false">✕</div>
      <div class="gift-header">
        <div class="left-box">
          <p class="ticket">我的球票 <span class="num">{{ userticket }}</span></p>
          <p class="tip">提示：连击可以连送</p>
        </div>
        <div class="btn" @click="handleSendGift">赠送</div>
      </div>
      <div class="gift-list">
        <div class="prev page-btn" :class="{ disabled: getCurrentIndex === 0 }" @click="handlePrev">
          <img class="icon" src="@/static/user/white-right-arrow-icon.png" />
          <img class="icon2" src="@/static/user/right-arrow-icon.png" />

        </div>
        <n-carousel draggable :show-arrow="false" :show-dots="false" :loop="false" ref="carouselRef">
          <div class="sub-list" v-for="(subItem, index) in groupGiftList" :key="index">
            <div class="item" v-for="item in subItem" :class="{ active: selectedGift.id === item.id }" :key="item.id"
              @click="selectGift(item)">
              <div class="gift">
                <img class="gift-icon" :src="item.url" />
              </div>
              <p class="name">{{ item.name }}</p>
              <div class="ticket">{{ item.ticket }} <img class="ticket-icon" src="@/static/user/ticket-icon.png" />
              </div>
            </div>
          </div>
        </n-carousel>
        <div class="next page-btn" :class="{ disabled: getCurrentIndex === groupGiftList.length - 1 }"
          @click="handleNext">
          <img class="icon" src="@/static/user/white-right-arrow-icon.png" />
          <img class="icon2" src="@/static/user/right-arrow-icon.png" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import * as live from '@/proto/proto.js'
import { useClickOutside } from '@/composables/useClickOutside'
import { useUserStore } from '@/stores/user'
import { findByRoomIdApi, getUserProhibitsApi, getuserBlockListApi, insertuserBlockApi, canceluserProhibitApi, insertUserProhibitApi, cancelUserProhibitApi, getSystemNoticeApi, getTeamLogoListApi, getSelectActiveGiftApi } from '@/api/match'
import { getUserManageApi, userGiveGiftApi } from '@/api/user'
import { useAuthStore } from '@/stores/auth';
import { useMessage } from 'naive-ui'
import { useTaskStore } from '@/stores/task'
import { Downloader, Parser, Player } from 'svga-web'
// import { throttleWithFirst, debounce, debounceWithFirst } from '@/utils/common'
const normalCardRef = ref(null)
const taskStore = useTaskStore()
import Level from '@/components/module/level/Level.vue'
const message = useMessage()
const authStore = useAuthStore()
const emit = defineEmits(['send-danmu'])
const userInfo = computed(() => userStore.userInfo)
const props = defineProps({
  roomId: {
    type: Number,
    default: 0
  },
})
const checkLogin = () => {
  if (!isLogin.value) {
    authStore.showLogin()
    return false
  }
}
//获取礼物列表
interface GiftItem {
  id: number;
  name: string;//礼物名称
  specialUrl: string;//礼物动效图标
  url: string;//礼物图标
  giftState: number;//礼物状态-0：下架 1：上架
  ticket: number;//价值球票数
}
let giftList = ref<GiftItem[]>([])
let groupGiftList = ref<GiftItem[][]>([])
let isShowGift = ref(true)
const getSelectActiveGift = async () => {
  try {
    let res = await getSelectActiveGiftApi()
    giftList.value = res.data;
    groupGiftList.value = chunkArray(res.data, 5);
    selectedGift.value = giftList.value[0];
  } catch (error) {
    console.log(error, '获取列表配置失败')
  }
}
const chunkArray = (array: any[], size: number) => {
  const result: any[] = []
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size))
  }
  return result
}
//通过ID匹配获取礼物信息
const getGiftInfo = (giftId) => {
  return giftList.value.find((item) => {
    return item.id == giftId
  })
}
let selectedGift = ref<GiftItem>({
  id: -1,
  name: '',
  specialUrl: '',
  url: '',
  giftState: 1,
  ticket: 0,
})
const userticket = computed(() => userStore.userInfo.ticket)
const selectGift = (item) => {
  selectedGift.value = item;
}
const carouselRef = ref<any>(null)
let getCurrentIndex = ref(0)
const handleNext = () => {
  console.log('carouselRef.value.getCurrentIndex()', carouselRef.value.getCurrentIndex())
  carouselRef.value.next()
  getCurrentIndex.value = carouselRef.value.getCurrentIndex()
}
const handlePrev = (aa) => {
  carouselRef.value.prev()
  getCurrentIndex.value = carouselRef.value.getCurrentIndex()
}
interface showGiftItem {
  userId: number,
  username: string,
  url: string,
  specialUrl: string,
  giftCount: number,
  timestamp: number,
  giftName: string
}
let giftShowList = ref<showGiftItem[]>([])
// 礼物动画定时器映射
const giftTimers = new Map()

// 添加礼物到动画列表
const addGiftToAnimation = (giftData) => {
  const { userId, username, url, specialUrl, giftCount = 1, giftName } = giftData
  const now = Date.now()
  const timerKey = `${userId}-${url}`

  // 查找是否存在相同用户的相同礼物
  const existingIndex = giftShowList.value.findIndex(item =>
    item.userId === userId && item.url === url
  )

  if (existingIndex !== -1) {
    // 更新现有礼物的数量和时间戳，直接修改属性避免触发插入动画
    const existingGift = giftShowList.value[existingIndex]
    existingGift.giftCount = (existingGift.giftCount || 1) + giftCount
    existingGift.timestamp = now

    // 清除旧的定时器
    if (giftTimers.has(timerKey)) {
      clearTimeout(giftTimers.get(timerKey))
    }
  } else {
    // 检查是否超过最大显示数量（5条）
    if (giftShowList.value.length >= 5) {
      // 移除最旧的礼物
      const oldestGift = giftShowList.value.shift()
      if (oldestGift) {
        const oldTimerKey = `${oldestGift.userId}-${oldestGift.url}`
        if (giftTimers.has(oldTimerKey)) {
          clearTimeout(giftTimers.get(oldTimerKey))
          giftTimers.delete(oldTimerKey)
        }
      }
    }

    // 添加新的礼物
    giftShowList.value.push({
      userId,
      username,
      url,
      giftName,
      specialUrl,
      giftCount,
      timestamp: now
    })

    // 如果当前没有播放动画，则开始播放
    if (!isPlaying.value) {
      // 如果是新添加的礼物，直接播放它
      currentPlayingIndex.value = giftShowList.value.length - 1
      initSvga()
    }
  }

  // 设置新的定时器，3秒后移除该礼物
  // const timer = setTimeout(() => {
  //   const index = giftShowList.value.findIndex(item =>
  //     item.userId === userId && item.url === url
  //   )
  //   if (index !== -1) {
  //     // 如果移除的礼物在当前播放索引之前，需要调整索引
  //     if (index < currentPlayingIndex.value) {
  //       currentPlayingIndex.value--
  //     }
  //     giftShowList.value.splice(index, 1)
  //   }
  //   giftTimers.delete(timerKey)
  // }, 3000)

  // giftTimers.set(timerKey, timer)
}
//svga 动画播放
let player = ref<Player | null>(null);
const downloader = new Downloader()
const parser = new Parser()
let currentPlayingIndex = ref(0) // 当前播放的礼物索引
let isPlaying = ref(false) // 是否正在播放

const initSvga = async () => {
  if (isPlaying.value || giftShowList.value.length === 0) {
    return;
  }

  // 如果当前索引超出范围，重置为0
  if (currentPlayingIndex.value >= giftShowList.value.length) {
    currentPlayingIndex.value = 0
  }

  const currentGift = giftShowList.value[currentPlayingIndex.value]
  if (!currentGift || !currentGift.specialUrl) {
    // 如果当前礼物没有特效URL，跳到下一个
    currentPlayingIndex.value++
    if (currentPlayingIndex.value < giftShowList.value.length) {
      initSvga()
    }
    return
  }

  isPlaying.value = true

  try {
    // 如果已有播放器，先清理
    if (player.value) {
      player.value.clear()
      player.value = null
    }

    // #canvas 是 HTMLCanvasElement
    player.value = new Player('#gift-canvas')
    const fileData = await downloader.get(currentGift.specialUrl)
    const svgaData = await parser.do(fileData)

    player.value.set({ loop: 1, cacheFrames: true })

    await player.value.mount(svgaData)

    player.value
      // 开始动画事件回调
      // .$on('start', () => console.log('SVGA start:', currentGift.username))
      // 暂停动画事件回调
      // .$on('pause', () => console.log('SVGA pause'))
      // 停止动画事件回调
      // .$on('stop', () => console.log('SVGA stop'))
      // 动画结束事件回调
      .$on('end', () => {
        isPlaying.value = false
        currentPlayingIndex.value++

        // 播放下一个礼物动画
        if (currentPlayingIndex.value < giftShowList.value.length) {
          setTimeout(() => {
            initSvga()
          }, 100) // 短暂延迟，确保状态更新
        } else {
          // 所有动画播放完毕，重置索引
          currentPlayingIndex.value = 0
          if (player.value) {
            player.value.clear()
          }
        }
      })

    // 开始播放动画
    player.value.start()

  } catch (error) {
    console.error('SVGA播放错误:', error)
    isPlaying.value = false
    currentPlayingIndex.value++
    // 出错时尝试播放下一个
    if (currentPlayingIndex.value < giftShowList.value.length) {
      setTimeout(() => {
        initSvga()
      }, 100)
    }
  }
  // player.clear()
}
const handleSendGift = async () => {
  const req = {
    liveId: props.roomId,
    roomId: props.roomId,
    giftId: selectedGift.value.id
  }
  let res = await userGiveGiftApi(req)
  userStore.userInfo.ticket = res.data;
}
//获取战队logo列表
let teamLogoList = ref<any[]>([])
const getTeamLogoList = async () => {
  try {
    let res = await getTeamLogoListApi()
    if (res.data.length > 0) {
      teamLogoList.value = res.data
    }
  } catch (error) {
    console.error('获取战队logo列表失败', error)
  }
}

//获取自己是否是管理员
// computed(() => props.roomId === userStore.userInfo.id)
const isHost = (userId) => {
  return userId !== userInfo.value.id && userId !== props.roomId && props.roomId === userInfo.value.id
}
const getIsManage = (userId) => {
  return userId !== userInfo.value.id && userId !== props.roomId && isManage.value
}
let isManage = ref(false)
const getUserManage = async () => {
  if (props.roomId)
    if (userStore.isLogin) {
      let res = await getUserManageApi({ roomId: props.roomId })
      console.log(res, '----------dsadasda')
      if (res.data) {
        isManage.value = res.data
      }
    }
}
//获取禁言列表
let prohibitList = ref<any[]>([])
const getUserProhibit = async () => {
  if (userStore.isLogin) {
    // let res = await getUserProhibitsApi({ roomId: props.roomId })
    let res = await getuserBlockListApi({ roomId: props.roomId })
    prohibitList.value = res.data
  }
}
// 判断用户是否禁言
const isProhibit = (userId) => {
  return prohibitList.value.some((item) => {
    return item.userId === userId
  })
}
//禁言用户
const handleShutUp = async (type) => {
  //ban 禁言  lift解禁
  if (type === 'ban') {
    await insertuserBlockApi({ roomId: props.roomId, userId: chatItem.value.user.id })
    getUserProhibit()
    message.success('禁言成功')
  } else {
    await canceluserProhibitApi({ roomId: props.roomId, userId: chatItem.value.user.id })
    getUserProhibit()
    message.success('解除成功')
  }
}
let chatItem = ref<ReceivedMessage>({
  type: 'TEXT',
  content: '',
  user: {
    id: 0,
    userName: '',
    avatar: '',
    level: '',
    tag: ''
  }
})
let isShowNoramlCard = ref(false)
const handleOpenNormalCard = (chat) => {
  isShowNoramlCard.value = true
  chatItem.value = chat
}
useClickOutside(
  normalCardRef,
  () => {
    isShowNoramlCard.value = false
  },
  {
    ignore: ['.user-name'], // 忽略用户名点击
    delay: 100, // 添加小延迟，提升用户体验
  }
)
//被挤掉后重新连接
const onReconnection = () => {
  console.log(connectionStatus.value, '----------connectionStatus.value')
  if (connectionStatus.value === 'connected') {
    return
  }
  connectWebSocket();
}
//初始化监听滚动事件
const initChatScroll = () => {
  const chatList = document.querySelector('.chat-list')
  if (chatList) {
    chatList.addEventListener('scroll', handleScroll)
  }
}
// 监听滚动事件
const handleScroll = () => {
  const chatList = document.querySelector('.chat-list')
  if (chatList) {
    // 根据是否在底部来设置 isNeedScroll
    isNeedScroll.value = !isScrolledToBottom()
    //清空新消息
    if (isScrolledToBottom()) {
      isNewMessage.value = false
    }

  }
}
//检查是否在底部
const isScrolledToBottom = (): boolean => {
  const chatList = document.querySelector('.chat-list')
  if (chatList) {
    // 当前滚动位置加上可视区域高度，接近于总滚动高度时认为是在底部
    // 这里用 1 作为误差范围，因为有时候会有小数点的误差
    return chatList.scrollHeight - chatList.scrollTop - chatList.clientHeight <= 1
  }
  return false
}
//滚动到最底部
let isNeedScroll = ref(false)
let isNewMessage = ref(false)
const scrollToBottom = () => {
  nextTick(() => {
    const chatList = document.querySelector('.chat-list')
    if (chatList) {
      chatList.scrollTop = chatList.scrollHeight
    }
  })
}
//根据状态判断是否滚动到底部
const scrollToBottomIfNeeded = () => {
  if (isScrolledToBottom()) {
    // 如果当前在底部，自动滚动到底部
    scrollToBottom();
  } else {
    // 如果不在底部，设置需要滚动标志
    isNeedScroll.value = true;
    isNewMessage.value = true;
  }
}
// 获取系统通知
const convertUrlToAnchor = (text: string): string => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.replace(urlRegex, '<a href="$1" target="_blank" class="link">$1</a>');
}
let noticeInterval = ref<NodeJS.Timeout | null>(null)
const getSystemNotice = async () => {
  try {
    let res = await getSystemNoticeApi({ type: 'live' })
    if (res.data.length === 0) {
      return
    }
    //1发送 2不发送
    if (res.data[0].state === 1) {
      const messageRecord = {
        type: 'NOTICE',
        content: res.data[0].content,
        messageId: generateMessageId(),
        user: {
          id: props.roomId,
          userName: '系统通知',
          avatar: '',
          level: '',
          tag: ''
        }
      }
      receivedMessages.value.push(messageRecord);
      // noticeInterval.value = setInterval(() => {
      //   receivedMessages.value.push(messageRecord);
      //   scrollToBottomIfNeeded();
      // }, Math.floor(60000 * (res.data[0].number ? res.data[0].number : 1)))
      scrollToBottomIfNeeded();
    }
  } catch (error) {
    console.log('获取系统通知失败', error)
  }

}
//查询聊天室近10条消息
const findByRoomId = async () => {
  let res = await findByRoomIdApi({ roomId: props.roomId })
  if (res.data.length > 0) {
    res.data.forEach((item) => {
      let msg = {
        type: 'TEXT',
        content: item.content,
        user: {
          id: item.user?.id,
          userName: item.user?.userName,
          avatar: item.user?.userImage,
          level: item.user?.grade,
          tag: item.user?.labelUrl
        }
      }
      receivedMessages.value.unshift(msg)
    })
    scrollToBottom()
  }
}
findByRoomId()
const userStore = useUserStore()
const isLogin = computed(() => userStore.isLogin)
const sendContent = ref('')
const websocket = ref<WebSocket | null>(null)
// 连接状态
const connectionStatus = ref('disconnected')
// 连接状态消息
const connectionStatusMessage = ref('未连接')
// 心跳定时器
const heartbeatTimer = ref<NodeJS.Timeout | null>(null)
// 心跳间隔时间
const heartbeatInterval = ref(15000)
// 接收到的消息
interface ReceivedMessage {
  content: string;
  type: string;
  timestamp?: number;
  giftCount?: number;
  user: {
    id: number,
    userName: string,
    avatar?: string,
    level?: string,
    tag?: string
  }
}
const receivedMessages = ref<ReceivedMessage[]>([])
const connectWebSocket = () => {
  // const url = 'ws://192.168.77.217:10710/';
  const url = 'wss://tapi.shenyantuling.com:31171/';
  // const url = 'ws://192.168.77.102:10710/'
  // 更新连接状态为连接中
  connectionStatus.value = 'connecting';
  connectionStatusMessage.value = '正在连接...';
  websocket.value = new WebSocket(url)

  websocket.value.onopen = () => {
    connectionStatus.value = 'connected'
    connectionStatusMessage.value = '已连接'
    // 启动心跳检测
    startHeartbeat()
    //是否登录
    if (userStore.isLogin) {
      sendLoginReq()
    } else {
      //游客进入
      touristEnterLiveRoom()
    }
  }

  websocket.value.onmessage = (event) => {
    try {
      // 更新连接状态
      connectionStatus.value = 'connected';
      connectionStatusMessage.value = '已连接';
      // Handle binary data
      if (event.data instanceof Blob) {
        // const reader = new FileReader();
        // reader.onload = () => {
        //   const buffer = new Uint8Array(reader.result as ArrayBuffer);
        //   handleReceivedMessage(buffer);
        // };
        // reader.readAsArrayBuffer(event.data);


        event.data.arrayBuffer().then(buffer => {
          const uint8Array = new Uint8Array(buffer);
          handleReceivedMessage(uint8Array);
        }).catch(error => {
          console.error('Error converting blob to array buffer:', error);
        });

      } else if (event.data instanceof ArrayBuffer) {
        const uint8Array = new Uint8Array(event.data);
        handleReceivedMessage(uint8Array);
      } else {
        console.log('Received non-binary message:', event.data);
      }
    } catch (error) {
      console.error('Error processing received message:', error);
    }
  }

  websocket.value.onerror = (error) => {
    console.error('WebSocket error:', error)
  }

  websocket.value.onclose = () => {
    connectionStatus.value = 'disconnected'
    connectionStatusMessage.value = '已断开'
    console.log('链接已断开')
    // 停止心跳检测
    stopHeartbeat()
  }
}
//处理接收到的消息
const handleReceivedMessage = (buffer) => {
  try {
    // 使用正确的路径解码消息
    const IMMessage = live.IMMessage;

    const decodedMessage = IMMessage.decode(buffer);
    // 使用toObject转换为友好的格式显示
    const jsonMessage = IMMessage.toObject(decodedMessage, {
      enums: String,  // 将枚举值显示为字符串
      longs: String,  // 将长整型显示为字符串
      bytes: String,  // 将字节显示为字符串
      defaults: true, // 显示默认值
      arrays: true,   // 保留空数组
      objects: true,  // 保留空对象
      oneofs: true    // 保留oneof字段信息
    });
    // 获取消息类型（从oneof字段中获取）
    let messageType = 'unknown';
    if (jsonMessage.body === 'loginResponse') {
      jsonMessage.loginResponse.success ? console.log('登录成功') : console.log('登录失败')
      //进入直播间
      enterLiveRoom()
    }
    //监听双开直播自动退出
    if (jsonMessage.body === 'logoutResponse') {
      // 退出直播间成功，清空消息列表
      // exitLiveRoom()
      const messageRecord = {
        type: 'RECONNECTION',
        content: '',
        messageId: jsonMessage.header.messageId,
        user: {
          id: props.roomId,
          userName: '系统',
          avatar: '',
          level: '',
          tag: ''
        }
      }
      receivedMessages.value.push(messageRecord);
      scrollToBottomIfNeeded();
    }
    if (jsonMessage.body === 'chatMessage') {
      console.log(jsonMessage, "jsonMessage")
      messageType = Object.keys(jsonMessage.body)[0];
      // const messageId = jsonMessage.header?.messageId || `msg_${Date.now()}`;
      let logo = {
        url: ''
      }
      if (jsonMessage.chatMessage.user.tag) {
        logo = teamLogoList.value.find((item) => {
          return item.id == jsonMessage.chatMessage.user.tag
        })
      }
      const messageRecord = {
        type: jsonMessage.chatMessage.chatMessageType,
        content: jsonMessage.chatMessage.content,
        messageId: jsonMessage.header.messageId,
        timestamp: Date.now(),
        giftCount: 0,
        user: {
          id: parseInt(jsonMessage.chatMessage.user.userId),
          userName: jsonMessage.chatMessage.user.username,
          avatar: jsonMessage.chatMessage.user.avatar,
          level: jsonMessage.chatMessage.user.level,
          tag: logo?.url
        }
      };

      if (messageRecord.type == 'GIFT') {
        // 查找是否存在同一用户送出的同一种礼物（最近3秒内）
        const now = Date.now();
        const existingGiftIndex = receivedMessages.value.findIndex(msg =>
          msg.type === 'GIFT' &&
          msg.user.id === messageRecord.user.id &&
          msg.content === messageRecord.content &&
          (now - (msg.timestamp || 0)) < 3000 // 3秒内的礼物可以连击
        );

        if (existingGiftIndex !== -1) {
          // 更新现有礼物的数量
          const existingGift = receivedMessages.value[existingGiftIndex];
          existingGift.giftCount = (existingGift.giftCount || 1) + 1;
          existingGift.timestamp = now; // 更新时间戳
          // 触发响应式更新
          receivedMessages.value.splice(existingGiftIndex, 1, { ...existingGift });
        } else {
          // 添加新的礼物消息
          messageRecord.giftCount = 1;
          messageRecord.timestamp = now;
          receivedMessages.value.push(messageRecord);
        }
        // 添加到礼物的动画列表
        const giftInfo = getGiftInfo(messageRecord.content)
        if (giftInfo) {
          addGiftToAnimation({
            userId: messageRecord.user.id,
            username: messageRecord.user.userName,
            url: giftInfo.url,
            giftName: giftInfo.name,
            specialUrl: giftInfo.specialUrl,
            giftCount: 1
          })
        }
      }

      if (messageRecord.type == 'TEXT') {
        receivedMessages.value.push(messageRecord);
        emit('send-danmu', messageRecord)
      }
      if (messageRecord.type == 'FOCUS') {
        receivedMessages.value.push(messageRecord);
      }
      if (messageRecord.type === 'ENTER') {
        if (!isShowEnter.value) {
          receivedMessages.value.push(messageRecord);
        }
      }

      scrollToBottomIfNeeded();
    }

    // 添加到消息列表
    // receivedMessages.value.unshift(messageRecord);
    // 保持最多20条消息
    // if (receivedMessages.value.length > 20) {
    //   receivedMessages.value = receivedMessages.value.slice(0, 20);
    // }
  } catch (error) {
    console.error('解码消息时出错:', error);
  }
}
// 发送心跳消息
const sendHeartbeat = () => {
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    try {
      const IMMessage = live.IMMessage;
      const Header = live.Header;

      // 创建消息头
      const header = Header.create({
        messageId: 'heartbeat_' + Date.now(),
        timestamp: Date.now(),
        version: 1
      });

      // 创建IM消息
      const imMessage = IMMessage.create({
        header: header,
        heartbeat: {
          type: 1,
          timestamp: Date.now()
        }
      });

      // 编码消息
      const buffer = IMMessage.encode(imMessage).finish();

      // 发送心跳消息
      websocket.value.send(buffer);
      // console.log('已发送心跳消息');
    } catch (error) {
      console.error('发送心跳消息失败:', error);
    }
  } else {
    console.warn('WebSocket未连接，无法发送心跳');
    // WebSocket已断开，尝试重新连接
    if (connectionStatus.value !== 'connecting') {
      console.log('检测到WebSocket已断开，尝试重新连接');
      reconnectWebSocket();
    }
  }
}
// 尝试重新连接WebSocket
const reconnectWebSocket = () => {
  // 确保先停止现有的心跳
  stopHeartbeat();
  connectWebSocket();
}
//停止心跳
const disconnectWebSocket = () => {
  // 停止心跳
  stopHeartbeat();
  if (websocket.value) {
    websocket.value.close();
    websocket.value = null;
    connectionStatus.value = 'disconnected';
    connectionStatusMessage.value = '未连接';
  }
}
// 开始心跳检测
const startHeartbeat = () => {
  // 先清除可能存在的定时器
  stopHeartbeat();

  // 创建新的定时器，每15秒发送一次心跳
  heartbeatTimer.value = setInterval(() => {
    sendHeartbeat();
  }, heartbeatInterval.value);

  console.log('心跳检测已启动，间隔：', heartbeatInterval, '毫秒');
}
// 停止心跳检测
const stopHeartbeat = () => {
  if (heartbeatTimer.value) {
    clearInterval(heartbeatTimer.value);
    heartbeatTimer.value = null;
    console.log('心跳检测已停止');
  }
}
//发送登录请求
const sendLoginReq = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  // Create the IMMessage with the header
  const imMessage = IMMessage.create({
    header: header,
    loginRequest: {
      token: userStore.userToken,
      devicePlatform: live.common.DevicePlatform.WEB
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();

  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('发送登录请求');
  }
}
//登录用户进入房间
const isShowEnter = ref(false)
const enterLiveRoom = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  const imMessage = IMMessage.create({
    header: header,
    enterLiveRoomEvent: {
      roomId: props.roomId.toString(),
      userType: live.live.UserType.SIGNED
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();

  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('发送进入直播间请求');
  }
}
//匿名用户进入房间
const touristEnterLiveRoom = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  const imMessage = IMMessage.create({
    header: header,
    enterLiveRoomEvent: {
      roomId: props.roomId.toString(),
      userType: live.live.UserType.ANONYMOUS
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();

  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('游客进入直播间请求');
  }
}
//退出直播间
const exitLiveRoom = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  const imMessage = IMMessage.create({
    header: header,
    exitLiveRoomEvent: {
      roomId: props.roomId.toString(),
      success: true,
      errorMessage: '退出直播间成功'
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();
  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('退出直播间成功');
  }
}
//关注主播
const followLiveRoom = () => {
  // 正确获取IMMessage构造函数
  const IMMessage = live.IMMessage;
  // 创建消息头
  const Header = live.Header;
  const header = Header.create({
    messageId: generateMessageId(),
    timestamp: Date.now(),
    version: 1
  });

  const imMessage = IMMessage.create({
    header: header,
    chatMessage: {
      chatMessageType: live.chat.ChatMessageType.FOCUS,
      senderId: userStore.userInfo.id.toString(),
      receiverId: props.roomId.toString(),
      content: '关注了主播',
      sequence: Date.now(),
      receiverType: live.chat.ReceiverType.LIVE_ROOM,
      extra: ''
    }
  });
  // Encode the message
  const buffer = IMMessage.encode(imMessage).finish();
  // Send the message if websocket is connected
  if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
    websocket.value.send(buffer);
    console.log('关注主播成功');
  }
}
//监听登录
watch(() => userStore.isLogin, (newVal) => {
  if (newVal) {
    sendLoginReq()
    getUserManage();
    //禁言列表
    getUserProhibit()
  }
})

//生成消息ID
const generateMessageId = () => {
  return 'msg_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}
//发送消息

const sendChatMessage = () => {
  if (!sendContent.value.trim()) {
    return;
  }
  try {
    // 正确获取IMMessage构造函数
    const IMMessage = live.IMMessage;
    // 创建消息头
    const Header = live.Header;
    const header = Header.create({
      messageId: generateMessageId(),
      timestamp: Date.now(),
      version: 1
    });

    // Create the IMMessage with the header
    const imMessage = IMMessage.create({
      header: header,
      chatMessage: {
        chatMessageType: live.chat.ChatMessageType.TEXT,
        senderId: userStore.userInfo.id.toString(),
        receiverId: props.roomId.toString(),
        content: sendContent.value.trim(),
        sequence: Date.now(),
        receiverType: live.chat.ReceiverType.LIVE_ROOM,
        extra: ''
      }
    });
    // Encode the message
    const buffer = IMMessage.encode(imMessage).finish();

    // Send the message if websocket is connected
    if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
      websocket.value.send(buffer);
      //情况消息
      sendContent.value = '';
      console.log('Message sent:', imMessage);
    } else {
      console.error('WebSocket is not connected');
    }
  } catch (error) {
    console.error('Error sending message:', error);
  }
}
const isThrottled = ref(false);
const handleKeyDown = (e) => {
  if (!e.shiftKey && e.keyCode == 13) {
    e.cancelBubble = true //ie阻止冒泡行为
    e.stopPropagation() //Firefox阻止冒泡行为
    e.preventDefault() //取消事件的默认动作*换行
    //以下处理发送消息代码
    throttledSendMessage()
  }
}
const throttledSendMessage = () => {
  //  检查消息内容
  if (!sendContent.value.trim()) {
    return
  }

  // 检查是否在冷却中
  if (isThrottled.value) {
    message.warning('发送太频繁了，请稍后再试')
    return
  }

  try {
    // 设置冷却状态
    isThrottled.value = true

    // 发送消息
    sendChatMessage()
    //每日任务
    taskStore.dailyBarrage()
    // 重置冷却状态
    setTimeout(() => {
      isThrottled.value = false
    }, 1500)
  } catch (error) {
    console.error('发送消息失败:', error)
    // 发送失败时也要重置状态
    isThrottled.value = false
  }
}

interface GiftItem {
  id: number;
  name: string;//礼物名称
  specialUrl: string;//礼物动效图标
  url: string;//礼物图标
  giftState: number;//礼物状态-0：下架 1：上架
  ticket: number;//价值球票数
}


onMounted(() => {
  //获取战队logo列表
  getTeamLogoList()
  //获取礼物配置
  getSelectActiveGift()
  connectWebSocket()
  getUserManage()
  getUserProhibit()
  //获取系统通知
  getSystemNotice()
  initChatScroll()
})

onBeforeUnmount(() => {
  // 退出直播间
  exitLiveRoom();
  // 确保在组件卸载时断开WebSocket连接
  disconnectWebSocket();
  // 确保心跳定时器被清除
  stopHeartbeat();
  connectionStatus.value = 'disconnected';
  connectionStatusMessage.value = '未连接';
  if (noticeInterval.value) {
    // 清除系统通知定时器
    clearInterval(noticeInterval.value);
  }
})
defineExpose({
  followLiveRoom
})
</script>
<style lang="scss" scoped>
.n-input {
  border: none !important;
}

// 礼物动画样式
.gift-animation-warp {
  position: absolute;
  top: 0;
  left: 0;
  // transform: translateY(-50%);
  z-index: 99;
  pointer-events: none;
  width: 100%;
  max-width: 400px;

  #gift-canvas {
    width: 120px;
    height: 120px;
    position: absolute;
    right: 0;
    top: 0;
  }

  .gift-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px 0;
  }

  .gift-item {
    position: relative;
    // background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
    max-width: 288px;
    width: fit-content;
    height: 52px;
    background-image: url("@/static/live/<EMAIL>");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    transform: translateX(-100%);
    animation: slideInGift 0.5s ease-out forwards;
    display: flex;
    align-items: center;
    padding: 0 60px 0 10px;

    &.gift-combo {
      animation: slideInGift 0.5s ease-out forwards, pulseCombo 0.3s ease-in-out 0.5s;
    }

    .gift-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      gap: 6px;
      color: white;
      // font-size: 14px;
      // font-weight: 500;

      .username {
        color: #ffffff;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 16px;
      }

      .action {
        color: white;
        opacity: 0.9;
        font-size: 16px;
      }

      .gift-icon {
        width: 32px;
        height: 32px;
        animation: bounceGift 0.6s ease-in-out infinite alternate;
      }

      .x {
        width: 24px;
        display: block;
      }

      .count {
        // color: #FFC300;
        // font-weight: 700;
        // font-size: 20px;
        // text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        // animation: scaleCount 0.3s ease-out;
        // line-height: 24px;
        // text-stroke: 1px #FFFFFF;
        // text-align: left;
        // font-style: normal;
        // text-transform: none;
        font-weight: 600;
        font-size: 20px;
        color: #FFC300;
        line-height: 24px;
        text-stroke: 1px #FFFFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        -webkit-text-stroke: 1px #FFFFFF;
      }
    }

    .gift-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 50%,
          rgba(255, 255, 255, 0.1) 100%);
      border-radius: 25px;
      opacity: 1;
      animation: shimmer 2s ease-in-out infinite;
    }
  }
}

// 动画定义
@keyframes slideInGift {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulseCombo {

  0%,
  100% {
    transform: translateX(0) scale(1);
  }

  50% {
    transform: translateX(0) scale(1.05);
  }
}

@keyframes bounceGift {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-2px);
  }
}

@keyframes scaleCount {
  0% {
    transform: scale(0.8);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {

  0%,
  100% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

// Vue transition 动画
.gift-slide-enter-active {
  transition: all 0.5s ease-out;
}

.gift-slide-leave-active {
  transition: all 0.3s ease-in;
}

.gift-slide-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.gift-slide-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.gift-slide-move {
  transition: transform 0.3s ease;
}

.chat-warp {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .normal-card {
    z-index: 700;
    position: absolute;
    width: 0;
    height: 0;
    top: 50%;
    left: 50%;
    filter: drop-shadow(0 0 6px rgba(0, 0, 0, .2));

    .sub-normal-card {
      position: relative;
      width: 280px;
      margin-left: -140px;
      margin-top: -50px;
      background: #fff;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      border-radius: 5px;
      padding: 10px;

      .close {
        position: absolute;
        right: 10px;
        top: 2px;
        color: #333;
        cursor: pointer;
      }

      .user-info {
        .identity {
          color: #fff;
          background-color: #FB2B1F;
          height: 20px;
          min-width: 32px;
          line-height: 20px;
          margin-right: 4px;
          font-size: 12px;
          border-radius: 4px;
          display: inline-flex;
          justify-content: center;
          vertical-align: super;
        }

        .nationality {
          display: inline-flex;
          vertical-align: baseline;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 4px;

          .icon {
            height: 20px;
            display: block;
          }
        }

        .level {
          display: inline-flex;
          vertical-align: top;
        }
      }

      .btns-box {
        margin-top: 4px;

        .btn {
          width: fit-content;
          line-height: 23px;
          font-size: 12px;
          border-radius: 2px;
          // vertical-align: middle;
          // text-align: center;
          // text-decoration: none;
          cursor: pointer;
          border: 1px solid #ddd;
          color: #666;
          padding: 0 8px;

          &:hover {
            color: #FB2B1F;
            -webkit-box-shadow: 0 0 8px #ddd;
            -moz-box-shadow: 0 0 8px #ddd;
            box-shadow: 0 0 8px #ddd;
          }
        }
      }
    }
  }

  .chat-list {
    flex: 1;
    background-color: #F4F4F4;
    padding: 10px;
    overflow: auto;
    position: relative;

    .chat-item {
      margin-bottom: 4px;

      //进入直播间
      .enter-box {
        display: flex;
        align-items: center;
        font-size: 14px;

        .label {
          color: #333333;
          background-color: #FA6D26;
          line-height: 1;
          font-size: 12px;
          padding: 2px;
          margin-right: 4px;
        }

        .user-name {
          color: #3177FD;
          margin-right: 4px;
        }

        .tip {
          color: #9E9E9E;
        }
      }

      //系统公告
      .notice-box {
        align-items: center;
        font-size: 14px;

        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          display: inline-block;
          vertical-align: sub;
        }

        .user-name {
          color: #3177FD;
          margin-right: 4px;
        }

        .tip {
          color: #333333;

          :deep(.link) {
            color: #3177FD;

            // text-decoration: none;
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      //消息提醒
      .reconnection-box {
        align-items: center;
        font-size: 14px;

        .icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          display: inline-block;
        }

        .user-name {
          color: #3177FD;
          margin-right: 4px;
        }

        .text {
          color: #333333;
          word-wrap: break-word;
          word-break: break-all;

          .tip {
            color: #3177FD;
            cursor: pointer;
          }
        }
      }

      .text-box {
        font-size: 14px;
        line-height: 1.4;

        .user-info {
          display: flex;
          align-items: center;
        }

        .identity {
          color: #fff;
          background-color: #FB2B1F;
          height: 20px;
          min-width: 32px;
          line-height: 20px;
          margin-right: 4px;
          font-size: 12px;
          border-radius: 4px;
          display: inline-flex;
          justify-content: center;
          vertical-align: super;
        }

        .nationality {
          display: inline-flex;
          vertical-align: baseline;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 4px;

          .icon {
            height: 20px;
            display: block;
          }
        }


        .level {
          display: inline-flex;
          vertical-align: top;
        }

        .user-name {
          color: #3177FD;
          margin-right: 4px;
          display: inline;
          line-height: 20px;
          position: relative;
          cursor: pointer;
          vertical-align: super;

          // &:hover {
          //   .handle-box {
          //     display: block;
          //   }
          // }

          // .handle-box {
          //   position: absolute;
          //   display: none;
          //   top: 100%;
          //   left: 0;
          //   padding-top: 2px;
          //   width: max-content;
          // }

          // .shut-up {
          //   color: #333;
          //   background-color: #fff;
          //   line-height: 1;
          //   font-size: 14px;
          //   padding: 6px 12px;
          //   border-radius: 4px;
          //   z-index: 9;
          //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          //   cursor: pointer;
          //   position: relative;

          //   &::before {
          //     content: '';
          //     position: absolute;
          //     top: 0;
          //     right: 50%;
          //     width: 6px;
          //     height: 6px;
          //     background-color: #fff;
          //     transform: translate(50%, -50%) rotate(45deg);
          //   }
          // }
        }

        .content {
          color: #333333;
          display: inline;
          line-height: 20px;
          word-wrap: break-word;
          word-break: break-all;
          vertical-align: super;

          &.focus {
            color: #FB2B1F;
          }
        }

        .gift-content {
          display: inline-flex;
          vertical-align: super;
          color: #FB2B1F;

          .gift-icon {
            height: 20px;
            display: block;
            margin: 0 4px;
          }
        }
      }
    }
  }

  .chat-bottom {
    padding: 10px;
    position: relative;

    .scroll-bottom {
      position: absolute;
      top: -38px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #FB2B1F;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #fff;
      width: 150px;
      height: 38px;
      border-radius: 32px;
      cursor: pointer;
      z-index: 9;

      .icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
      }
    }

    .operate-box {
      display: flex;
      padding-bottom: 6px;

      .shield {
        cursor: pointer;
        margin-right: 10px;


        .icon {
          display: block;
          width: 24px;
          height: 24px;
        }
      }

      .qr-code-popup {
        background: #ffffff;
        border: 1px solid var(--color-gray-200);
        border-radius: 8px;
        padding: 16px;
        box-shadow: var(--boxshadow-medium);

        .qr-placeholder {
          width: 120px;
          height: 120px;
          background: var(--color-gray-200);
          border-radius: 4px;
          margin-bottom: 8px;
        }

        .label {
          margin: 0;
          font-size: 12px;
          color: #818181;

          p {
            text-align: center;
          }
        }
      }
    }

    .chat-input-warp {
      display: flex;

      .input-box {
        display: flex;
        flex: 1;
        height: 50px;
        align-items: center;
        position: relative;
        border: 1px solid #D9D9D9;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;

        .textarea-box {
          flex: 1;
          height: 100%;
        }

        .chat-textarea {
          width: 100%;
          padding: 10px;
          box-sizing: border-box;
          font-size: 12px;
          border: none;
          color: #333;
          border-radius: 0;
          resize: none;
          flex: 1;
          outline: none;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }

        .not-login {
          font-size: 14px;
          cursor: pointer;
          padding-left: 20px;

          .label {
            color: #FF2F2E;
          }
        }
      }



      .send-btn {
        width: 60px;
        background: #FB2B1F;
        color: #fff;
        border: none;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;

        &.disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }

  .gift-warp {
    position: absolute;
    width: 100%;
    // height: 100%;
    bottom: 0;
    left: 0;
    filter: drop-shadow(0 0 6px rgba(0, 0, 0, .2));
    background-color: #fff;

    .close {
      position: absolute;
      right: 10px;
      top: 0px;
      cursor: pointer;
      font-weight: bold;
      font-size: 12px;
      color: #333;
    }

    .gift-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      border-bottom: 1px solid #F5F5F5;

      .left-box {
        .ticket {
          font-size: 14px;
          color: #333;

          .num {
            color: #FB2B1F;
            margin-left: 8px;
          }
        }

        .tip {
          color: #818181;
          font-size: 12px;
        }
      }

      .btn {
        width: 60px;
        height: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-radius: 4px;
        background-color: #FB2B1F;
        font-size: 12px;
        color: #fff;
        margin-right: 20px;
      }
    }

    .gift-list {
      position: relative;

      .page-btn {
        position: absolute;
        width: 16px;
        height: 44px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #FB2B1F;
        border-radius: 4px;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 9;

        .icon {
          display: block;
          width: 14px;
          height: 14px;
        }

        .icon2 {
          width: 14px;
          height: 14px;
          display: none;
        }

        &.prev {
          left: 2px;
          top: 50%;


          .icon {
            transform: rotate(180deg);
          }

        }

        &.next {
          right: 2px;
          top: 50%;
        }

        &.disabled {
          background-color: #F2F2F2;

          .icon {
            display: none;
          }

          .icon2 {
            display: block;
          }
        }
      }



      .sub-list {
        display: flex;
        padding: 0 20px;
      }

      .item {
        font-size: 14px;
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 5px 0;
        cursor: pointer;

        &.active {
          .gift {
            border-color: #FB2B1F;
          }
        }

        .gift {
          border: 2px solid #fff;
          border-radius: 4px;

          .gift-icon {
            display: block;
            width: 50px;
            height: 50px;
            border-radius: 4px;
          }
        }

        .name {
          font-size: 12px;
          margin: 4px 0 0;
        }

        .ticket {
          color: #FB2B1F;
          font-size: 12px;
          display: flex;
          align-items: center;

          .ticket-icon {
            width: 16px;
            height: 16px;
            display: block;
            margin-left: 2px;
          }
        }
      }
    }
  }
}
</style>