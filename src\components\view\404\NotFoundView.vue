<script setup lang="ts">
import { useRouter } from 'vue-router'
const router = useRouter()
const goBack = () => {
  router.push({
    name: 'home'
  })
}
</script>
<template>
  <div class="container">
    <n-result status="404" title="404 资源不存在" description="生活总归带点荒谬">
      <template #footer>
        <n-button @click="goBack">回到首页</n-button>
      </template>
    </n-result>
  </div>
</template>
<style>
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
