<template>
  <div class="all-center-wrapper">
    <div class="all-nav-bar">
      <div class="content">
        <div class="li" :class="{ active: activeCategory === 'all' }" @click="changeCategory('all')">
          <span>全部</span>
        </div>
        <div class="li" :class="{ active: activeCategory === 'foot' }" @click="changeCategory('foot')">
          <span>足球</span>
        </div>
        <div class="li" :class="{ active: activeCategory === 'basket' }" @click="changeCategory('basket')">
          <span>篮球</span>
        </div>
        <div class="li" :class="{ active: activeCategory === 'lol' }" @click="changeCategory('lol')">
          <span>英雄联盟</span>
        </div>
        <div class="li" :class="{ active: activeCategory === 'dota' }" @click="changeCategory('dota')">
          <span>DOTA2</span>
        </div>
        <div class="li" :class="{ active: activeCategory === 'csgo' }" @click="changeCategory('csgo')">
          <span>CS:GO</span>
        </div>
        <div class="li" :class="{ active: activeCategory === 'hok' }" @click="changeCategory('hok')">
          <span>王者荣耀</span>
        </div>
      </div>
    </div>
    <div v-if="liveData.length > 0" class="all-center">
      <LiveItem :live-data="item" v-for="item in liveData" :key="item.matchId" />
    </div>
    <div v-else class="none-box">
      <none-box text="暂无比赛直播" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import LiveItem from '@/components/module/live-item/LiveItem.vue'
import noneBox from '@/components/module/none-box/index.vue'
import { getLivePageApi, getLivePageByTypeApi } from '@/api/index'

const route = useRoute()
const liveData = ref<any[]>([])
let activeCategory = ref('all') // 默认选中"全部"

// 获取直播列表
const getLivePage = async () => {
  try {
    let { data } = activeCategory.value === 'all'
      ? await getLivePageApi({ current: 1, size: 150 })
      : await getLivePageByTypeApi({ liveTypeEnum: activeCategory.value })
    liveData.value = activeCategory.value === 'all' ? data.records : data
  } catch (error) {
    console.error('获取直播数据失败:', error)
    liveData.value = []
  }
}

// 切换分类
const changeCategory = (category: string) => {
  activeCategory.value = category
  getLivePage()
}
onMounted(() => {
  activeCategory.value = route.query.type as string || 'all'
  getLivePage()
})
</script>

<style lang="scss" scoped>
.all-center-wrapper {
  width: 100%;
  background: #f5f5f5;
}

.all-nav-bar {
  background-color: #333333;
  height: 60px;
  width: 100%;

  .content {
    width: 1200px;
    height: 100%;
    display: flex;
    margin: 0 auto;

    .li {
      height: 100%;
      color: #fff;
      margin-right: 30px;
      font-size: 16px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        span {
          border-bottom: 2px solid #FB2B1F;
        }
      }

      &.active {
        span {
          border-bottom: 2px solid #FB2B1F;
        }
      }

      span {
        padding: 10px 0;
        border-bottom: 2px solid transparent;
        transition: border-bottom 0.3s ease;
      }
    }
  }
}

.all-center {
  width: 1200px;
  margin: 0 auto;
  padding: 40px 0;
  gap: 20px;
  display: flex;
  min-height: 600px;
  flex-wrap: wrap;
}

.none-box {
  height: 600px;
  background-color: #fff;
}
</style>