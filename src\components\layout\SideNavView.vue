<template>
  <div class="right-side-nav">
    <div class="side-item" @click="handleBackTop" v-show="showBackTop">
      <div class="icon">
        <img class="off" src="@/static/img/<EMAIL>" />
        <img class="on" src="@/static/img/<EMAIL>" />
      </div>
      <p class="text">返回顶部</p>
    </div>
    <n-popover trigger="hover" :overlap="false" placement="left">
      <template #trigger>
        <div class="side-item">
          <div class="icon">
            <img class="off" src="@/static/img/<EMAIL>" />
            <img class="on" src="@/static/img/<EMAIL>" />
          </div>
          <p class="text">APP下载</p>
        </div>
      </template>
      <div class="qrcode-popover">
        <img class="icon" src="@/static/live/<EMAIL>" />
        <p>用手机浏览器扫一扫</p>
        <p>精彩马上呈现</p>
      </div>
    </n-popover>
    <div class="side-item" @click="handleFeedback">
      <div class="icon">
        <img class="off" src="@/static/img/<EMAIL>" />
        <img class="on" src="@/static/img/<EMAIL>" />
      </div>
      <p class="text">意见反馈</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import router from '@/router'
const userStore = useUserStore()
const authStore = useAuthStore()
const isLogin = computed(() => userStore.isLogin)
const showBackTop = ref(false)

// 监听页面滚动
const handleScroll = () => {
  showBackTop.value = window.scrollY > 100
}

// 组件挂载时添加滚动监听
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 初始化按钮状态
  handleScroll()
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
const handleBackTop = () => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth'
  })
}
const handleFeedback = () => {
  if (!isLogin.value) {
    authStore.showLogin()
    return false
  } else {
    router.push('/user/feedback')
  }
}
</script>
<style lang="scss" scoped>
.qrcode-popover {
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 100px;
    height: 100px;
    margin-bottom: 20px;
  }

  p {
    font-size: 12px;
    color: #818181;
  }
}

.right-side-nav {
  position: fixed;
  z-index: 1000;
  right: 0;
  top: 50%;
  transform: translateY(-70%);
  background: #fff;
  width: 80px;
  padding: 14px 7px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, .2);
  border-radius: 6px 0 0 6px;
  gap: 28px;
  display: flex;
  flex-direction: column;

  .side-item {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &:hover {
      .text {
        color: #FF2F2E;
      }

      .icon {
        .off {
          display: none;
        }

        .on {
          display: block;
        }
      }
    }

    .icon {
      img {
        display: block;
        width: 32px;
        height: 32px;
      }

      .on {
        display: none;
      }
    }

    .text {
      margin-top: 4px;
      font-size: 16px;
      color: #818181;
    }
  }
}
</style>