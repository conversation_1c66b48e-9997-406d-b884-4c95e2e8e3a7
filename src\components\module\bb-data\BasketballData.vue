<template>
  <div class="live-data-view">
    <!-- 积分排名 -->
    <div class="data-section" v-if="homeRankingData?.matches > 0 && awayRankingData?.matches > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">积分排名</span>
      </div>
      <div class="ranking-section">
        <div class="ranking-table">
          <div class="team-ranking">
            <div class="team-header">
              <img class="team-logo" :src="liveDetail.awayLogo" />
              <span class="team-name">{{ liveDetail.awayName }}</span>
            </div>
            <div class="ranking-stats">
              <div class="ranking-table-content">
                <div class="ranking-header">
                  <span>赛</span>
                  <span>胜</span>
                  <span>负</span>
                  <span>得分</span>
                  <span>失分</span>
                  <span>净胜分</span>
                  <span>排名</span>
                  <span>胜率</span>
                </div>
                <div class="ranking-row">
                  <span class="rank">{{ awayRankingData?.matches || 0 }}</span>
                  <span class="wins">{{ awayRankingData?.win || 0 }}</span>
                  <span class="losses">{{ (awayRankingData?.matches || 0) - (awayRankingData?.win || 0) }}</span>
                  <!-- :class="{ red: isMaxNumber(homeRankingData.pointsFor || 120.5, [120.5, 107.6]) }" -->
                  <span class="points-for">{{ awayRankingData?.matches ? (awayRankingData.points /
                    awayRankingData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="points-against">{{ awayRankingData?.matches ? (awayRankingData.pointsAgainst /
                    awayRankingData.matches).toFixed(1) : '0.0'
                  }}</span>
                  <span>{{ awayRankingData?.matches ? (parseFloat((awayRankingData.points /
                    awayRankingData.matches).toFixed(1)) -
                    parseFloat((awayRankingData.pointsAgainst / awayRankingData.matches).toFixed(1))).toFixed(1) : '0.0'
                  }}</span>
                  <!-- :class="{ red: isMaxNumber(homeRankingData.pointDiff || 12.9, [12.9, 2.3]) }" -->
                  <!-- <span class="point-diff">{{homeRankingData.pointDiff || 12.9 }}</span> -->
                  <span class="wins">{{ awayRankingData?.position || 0 }}</span>
                  <!-- :class="{ red: isMaxNumber(parseFloat((homeRankingData.winRate || '82.9%').replace('%', '')), [82.9, 61.0]) }" -->
                  <span class="win-rate">{{ awayRankingData?.matches ? (awayRankingData.win / awayRankingData.matches *
                    100).toFixed(1) : '0.0' }}%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="team-ranking">
            <div class="team-header">
              <img class="team-logo" :src="liveDetail.homeLogo" />
              <span class="team-name">{{ liveDetail.homeName }}</span>
            </div>
            <div class="ranking-stats">
              <div class="ranking-table-content">
                <div class="ranking-header">
                  <span>赛</span>
                  <span>胜</span>
                  <span>负</span>
                  <span>得分</span>
                  <span>失分</span>
                  <span>净胜分</span>
                  <span>排名</span>
                  <span>胜率</span>
                </div>
                <div class="ranking-row">
                  <span class="rank">{{ homeRankingData?.matches || 0 }}</span>
                  <span class="wins">{{ homeRankingData?.win || 0 }}</span>
                  <span class="losses">{{ (homeRankingData?.matches || 0) - (homeRankingData?.win || 0) }}</span>
                  <!-- :class="{ red: isMaxNumber(homeRankingData.pointsFor || 120.5, [120.5, 107.6]) }" -->
                  <span class="points-for">{{ homeRankingData?.matches ? (homeRankingData.points /
                    homeRankingData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="points-against">{{ homeRankingData?.matches ? (homeRankingData.pointsAgainst /
                    homeRankingData.matches).toFixed(1) : '0.0'
                  }}</span>
                  <span>{{ homeRankingData?.matches ?
                    (parseFloat((homeRankingData.points / homeRankingData.matches).toFixed(1)) -
                      parseFloat((homeRankingData.pointsAgainst / homeRankingData.matches).toFixed(1))).toFixed(1) : '0.0'
                  }}</span>
                  <!-- :class="{ red: isMaxNumber(homeRankingData.pointDiff || 12.9, [12.9, 2.3]) }" -->
                  <!-- <span class="point-diff">{{homeRankingData.pointDiff || 12.9 }}</span> -->
                  <span class="wins">{{ homeRankingData?.position || 0 }}</span>
                  <!-- :class="{ red: isMaxNumber(parseFloat((homeRankingData.winRate || '82.9%').replace('%', '')), [82.9, 61.0]) }" -->
                  <span class="win-rate">{{ homeRankingData?.matches ? (homeRankingData.win / homeRankingData.matches *
                    100).toFixed(1) : '0.0' }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 场均数据 -->
    <div class="data-section" v-if="homeAverageData?.matches > 0 && awayAverageData?.matches > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">场均数据</span>
      </div>
      <div class="average-data-section">
        <div class="average-data-table">
          <!-- 客队场均数据 -->
          <div class="team-average">
            <div class="team-header">
              <img class="team-logo" :src="liveDetail.awayLogo" />
              <span class="team-name">{{ liveDetail.awayName }}</span>
            </div>
            <div class="average-stats">
              <div class="average-table-content">
                <div class="average-header">
                  <span>场均得分</span>
                  <span>篮板</span>
                  <span>助攻</span>
                  <span>抢断</span>
                  <span>盖帽</span>
                  <span>投篮命中率</span>
                  <span>三分球命中率</span>
                  <span>罚球命中率</span>
                </div>
                <div class="average-row">
                  <span class="avg-points">{{ awayAverageData?.matches ? (awayAverageData.points /
                    awayAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="rebounds">{{ awayAverageData?.matches ? (awayAverageData.rebounds /
                    awayAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="assists">{{ awayAverageData?.matches ? (awayAverageData.assists /
                    awayAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="steals">{{ awayAverageData?.matches ? (awayAverageData.steals /
                    awayAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="blocks">{{ awayAverageData?.matches ? (awayAverageData.blocks /
                    awayAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="fg-percentage">{{ awayAverageData?.fieldGoalsAccuracy || '0' }}%</span>
                  <span class="three-pt-percentage">{{ awayAverageData?.threePointersAccuracy || '0' }}%</span>
                  <span class="ft-percentage">{{ awayAverageData?.freeThrowsAccuracy || '0' }}%</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 主队场均数据 -->
          <div class="team-average">
            <div class="team-header">
              <img class="team-logo" :src="liveDetail.homeLogo" />
              <span class="team-name">{{ liveDetail.homeName }}</span>
            </div>
            <div class="average-stats">
              <div class="average-table-content">
                <div class="average-header">
                  <span>场均得分</span>
                  <span>篮板</span>
                  <span>助攻</span>
                  <span>抢断</span>
                  <span>盖帽</span>
                  <span>投篮命中率</span>
                  <span>三分球命中率</span>
                  <span>罚球命中率</span>
                </div>
                <div class="average-row">
                  <span class="avg-points">{{ homeAverageData?.matches ? (homeAverageData.points /
                    homeAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="rebounds">{{ homeAverageData?.matches ? (homeAverageData.rebounds /
                    homeAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="assists">{{ homeAverageData?.matches ? (homeAverageData.assists /
                    homeAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="steals">{{ homeAverageData?.matches ? (homeAverageData.steals /
                    homeAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="blocks">{{ homeAverageData?.matches ? (homeAverageData.blocks /
                    homeAverageData.matches).toFixed(1) : '0.0' }}</span>
                  <span class="fg-percentage">{{ homeAverageData?.fieldGoalsAccuracy || '0' }}%</span>
                  <span class="three-pt-percentage">{{ homeAverageData?.threePointersAccuracy || '0' }}%</span>
                  <span class="ft-percentage">{{ homeAverageData?.freeThrowsAccuracy || '0' }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 历史交锋 -->
    <div class="data-section" v-if="historyStats?.totalMatches > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">历史交锋</span>
      </div>
      <div class="history-section">
        <div class="history-header">
          <div class="top">
            <div class="logo">
              <img class="team-logo" :src="liveDetail.homeLogo" />
              <span class="team-name">{{ liveDetail.homeName }}</span>
            </div>
            <div class="switch-buttons">
              <div class="switch-btn" :class="{ active: historyMatchCount === 10 }" @click="switchHistoryCount(10)">10场
              </div>
              <div class="switch-btn" :class="{ active: historyMatchCount === 20 }" @click="switchHistoryCount(20)">20场
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="stats">
              <span class="stat-item first">
                <p class="label">近</p>
                <p>{{ historyStats.totalMatches }}场</p>
              </span>
              <span class="stat-item">
                <!-- {{ historyStats.draws }}平 -->
                <p class="label">胜率<span class="number">{{ historyStats.winRate }}</span></p>
                <p>{{ historyStats.wins }}胜{{ historyStats.losses }}负</p>
              </span>
              <span class="stat-item">
                <p class="label">赢率<span class="number">{{ historyStats.handicapWinRate }}</span></p>
                <p>{{ historyStats.winHandicap }}赢{{ historyStats.drawHandicap }}走{{ historyStats.loseHandicap }}输</p>
              </span>
              <span class="stat-item">
                <p class="label">大率<span class="number">{{ historyStats.bigBallRate }}</span></p>
                <p>{{ historyStats.bigBall }}大{{ historyStats.drawBall }}走{{ historyStats.smallBall }}小</p>
              </span>
              <span class="stat-item">
                <p class="label">单率<span class="number">{{ historyStats.oddBallRate }}</span>
                </p>
                <p>{{ historyStats.oddBall }}单{{ historyStats.evenBall }}双</p>
              </span>
            </div>
          </div>
        </div>
        <div class="history-matches">
          <div class="match-item">
            <span class="date">日期/赛事</span>
            <span class="away">客队</span>
            <span class="score">比分</span>
            <span class="home">主队</span>
            <span class="half">让分</span>
            <span class="size">总分</span>
            <!-- <span class="corner">角球</span> -->
          </div>
          <div class="match-item" v-for="item in switchHistoryData" :key="item.id">
            <span class="date">{{ dayjs(item.matchTime).format('YYYY-MM-DD') }}<br />{{ item.aliasName }}</span>
            <span class="away">{{ item.awayName }}</span>
            <span class="score">
              <p :class="[getScoreClass(item)]">{{ item.awayScore + item.awayOverScore }}-{{
                item.homeScore + item.homeOverScore }}</p>
              ({{ item.awayHalfScore }}-{{ item.homeHalfScore }})
            </span>
            <span class="home">{{ item.homeName }}</span>
            <span class="half"
              :class="[{ red: item.asianStatus === '2' }, { green: item.asianStatus === '1' }, { blue: item.asianStatus === '3' }]">{{
                item.asianPlate }}<br />{{ getAsianStatusName(item.asianStatus) }}</span>
            <span class="corner"
              :class="[{ red: item.sizeStatus === '2' }, { green: item.sizeStatus === '1' }, { blue: item.sizeStatus === '3' }]">{{
                item.sizePlate }}<br />{{
                getSizePlateName(item.sizeStatus) }}</span>
            <!-- <span class="corner">{{ (item.homeCornerKick ? item.homeCornerKick : 0) + (item.awayCornerKick ?
              item.awayCornerKick : 0) }}<br />{{ item.homeCornerKick }}-{{
                item.awayCornerKick }}</span> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 近期战绩 -->
    <div class="data-section" v-if="awaySwitchHistoryData.length > 0 || homeSwitchHistoryData.length > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">近期战绩</span>
      </div>
      <div class="recent-records">
        <div class="record-table">
          <div class="history-header">
            <div class="top">
              <div class="logo">
                <img class="team-logo" :src="liveDetail.awayLogo" />
                <span class="team-name">{{ liveDetail.awayName }}</span>
              </div>
              <div class="switch-buttons">
                <div class="switch-btn" :class="{ active: awayMatchCount === 10 }" @click="switchAwayCount(10)">
                  10场
                </div>
                <div class="switch-btn" :class="{ active: awayMatchCount === 20 }" @click="switchAwayCount(20)">
                  20场
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="stats">
                <span class="stat-item first">
                  <p class="label">近</p>
                  <p>{{ awayStats.totalMatches }}场</p>
                </span>
                <span class="stat-item">
                  <!-- {{ awayStats.draws }}平 -->
                  <p class="label">胜率<span class="number">{{ awayStats.winRate }}</span></p>
                  <p>{{ awayStats.wins }}胜{{ awayStats.losses }}负</p>
                </span>
                <span class="stat-item">
                  <p class="label">赢率<span class="number">{{ awayStats.handicapWinRate }}</span></p>
                  <p>{{ awayStats.winHandicap }}赢{{ awayStats.drawHandicap }}走{{ awayStats.loseHandicap }}输</p>
                </span>
                <span class="stat-item">
                  <p class="label">大率<span class="number">{{ awayStats.bigBallRate }}</span></p>
                  <p>{{ awayStats.bigBall }}大{{ awayStats.drawBall }}走{{ awayStats.smallBall }}小</p>
                </span>
                <span class="stat-item">
                  <p class="label">单率<span class="number">{{ awayStats.oddBallRate }}</span>
                  </p>
                  <p>单{{ awayStats.oddBall }}双{{ awayStats.evenBall }}</p>
                </span>
              </div>
            </div>
          </div>
          <div class="record-matches">
            <div class="match-header">
              <span>日期/赛事</span>
              <span>客队</span>
              <span>比分</span>
              <span>主队</span>
              <span>让分</span>
              <span>总分</span>
              <!-- <span>角球</span> -->
            </div>
            <div class="match-row" v-for="item in awaySwitchHistoryData" :key="item.id">
              <span class="date">{{ dayjs(item.matchTime).format('YYYY-MM-DD') }}<br />{{ item.aliasName }}</span>
              <span class="away">{{ item.awayName }}</span>

              <!-- <span class="score" :class="{ red: getScoreClass(item) }">{{ item.homeScore }}-{{ item.awayScore
              }}<br />({{
                  item.homeHalfScore }}-{{
                  item.awayHalfScore }})</span> -->
              <span class="score">
                <p :class="[getScoreClassByTeam(item, liveDetail.awayName)]">{{ item.awayScore + item.awayOverScore
                }}-{{
                    item.homeScore + item.homeOverScore }}
                </p>
                ({{ item.awayHalfScore }}-{{ item.homeHalfScore }})
              </span>
              <span class="home">{{ item.homeName }}</span>
              <span class="handicap"
                :class="[{ red: item.asianStatus === '2' }, { green: item.asianStatus === '1' }]">{{
                  item.asianPlate }}<br />{{ getAsianStatusName(item.asianStatus) }}</span>
              <span class="corner"
                :class="[{ red: item.sizeStatus === '2' }, { green: item.sizeStatus === '1' }, { blue: item.sizeStatus === '3' }]">{{
                  item.sizePlate }}<br />{{
                  getSizePlateName(item.sizeStatus) }}</span>
              <!-- <span class="corner">{{ (item.homeCornerKick ? item.homeCornerKick : 0) + (item.awayCornerKick ?
                item.awayCornerKick : 0) }}<br />{{ item.homeCornerKick }}-{{
                  item.awayCornerKick }}</span> -->
            </div>
          </div>
        </div>
        <div class="record-table">
          <div class="history-header">
            <div class="top">
              <div class="logo">
                <img class="team-logo" :src="liveDetail.homeLogo" />
                <span class="team-name">{{ liveDetail.homeName }}</span>
              </div>
              <div class="switch-buttons">
                <div class="switch-btn" :class="{ active: homeMatchCount === 10 }" @click="switchHomeCount(10)">
                  10场
                </div>
                <div class="switch-btn" :class="{ active: homeMatchCount === 20 }" @click="switchHomeCount(20)">
                  20场
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="stats">
                <span class="stat-item first">
                  <p class="label">近</p>
                  <p>{{ homeStats.totalMatches }}场</p>
                </span>

                <span class="stat-item">
                  <!-- {{ homeStats.draws }}平 -->
                  <p class="label">胜率<span class="number">{{ homeStats.winRate }}</span></p>
                  <p>{{ homeStats.wins }}胜{{ homeStats.losses }}负</p>
                </span>
                <span class="stat-item">
                  <p class="label">赢率<span class="number">{{ homeStats.handicapWinRate }}</span></p>
                  <p>{{ homeStats.winHandicap }}赢{{ homeStats.drawHandicap }}走{{ homeStats.loseHandicap }}输</p>
                </span>
                <span class="stat-item">
                  <p class="label">大率<span class="number">{{ homeStats.bigBallRate }}</span></p>
                  <p>{{ homeStats.bigBall }}大{{ homeStats.drawBall }}走{{ homeStats.smallBall }}小</p>
                </span>
                <span class="stat-item">
                  <p class="label">单率<span class="number">{{ homeStats.oddBallRate }}</span>
                  </p>
                  <p>单{{ homeStats.oddBall }}双{{ homeStats.evenBall }}</p>
                </span>
              </div>
            </div>
          </div>
          <div class="record-matches">
            <div class="match-header">
              <span>日期/赛事</span>
              <span>客队</span>
              <span>比分</span>
              <span>主队</span>
              <span>让分</span>
              <span>总分</span>
              <!-- <span>角球</span> -->
            </div>
            <div class="match-row" v-for="item in homeSwitchHistoryData" :key="item.id">
              <span class="date">{{ dayjs(item.matchTime).format('YYYY-MM-DD') }}<br />{{ item.aliasName }}</span>
              <span class="away">{{ item.awayName }}</span>

              <!-- <span class="score" :class="{ red: getScoreClass(item) }">{{ item.homeScore }}-{{ item.awayScore
              }}<br />({{
                  item.homeHalfScore }}-{{
                  item.awayHalfScore }})</span> -->
              <span class="score">
                <p :class="[getScoreClassByTeam(item, liveDetail.homeName)]">{{ item.awayScore + item.awayOverScore
                }}-{{
                    item.homeScore + item.homeOverScore }}
                </p>
                ({{ item.awayHalfScore }}-{{ item.homeHalfScore }})
              </span>
              <span class="home">{{ item.homeName }}</span>
              <span class="handicap"
                :class="[{ red: item.asianStatus === '2' }, { green: item.asianStatus === '1' }]">{{
                  item.asianPlate }}<br />{{ getAsianStatusName(item.asianStatus) }}</span>
              <!-- <span class="total red">{{ getSizePlateName(item.sizeStatus) }}</span> -->
              <span class="corner"
                :class="[{ red: item.sizeStatus === '2' }, { green: item.sizeStatus === '1' }, { blue: item.sizeStatus === '3' }]">{{
                  item.sizePlate }}<br />{{
                  getSizePlateName(item.sizeStatus) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 伤停情况 -->
    <div class="data-section" v-if="injuryList.length > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">伤停情况</span>
      </div>
      <div class="injury-section">
        <div class="injury-table">
          <div class="injury-header">
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
          <div class="injury-list">
            <div class="injury-header-row">
              <span>球员</span>
              <span>位置</span>
              <span>原因</span>
            </div>
            <template v-for="item in injuryList" :key="item.id">
              <div class="injury-row" v-if="item && item.type === 1">
                <div class="player-info">
                  <img class="player-avatar" :src="item.playerLogo || defaultPlayerLogo" />
                  <span class="player-name">{{ item.playerName }}</span>
                </div>
                <span class="position">{{ getPositionName(item.position) }}</span>
                <span class="reason">{{ item.reason }}</span>
              </div>
            </template>
            <div class="data-none" v-if="injuryList.findIndex(item => item.type === 1) === -1">
              无伤病信息
            </div>
          </div>
        </div>
        <div class="injury-table">
          <div class="injury-header">
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div class="injury-list">
            <div class="injury-header-row">
              <span>球员</span>
              <span>位置</span>
              <span>原因</span>
            </div>
            <template v-for="item in injuryList" :key="item.id">
              <div class="injury-row" v-if="item && item.type === 2">
                <div class="player-info">
                  <img class="player-avatar" :src="item.playerLogo || defaultPlayerLogo" />
                  <span class="player-name">{{ item.playerName }}</span>
                </div>
                <span class="position">{{ getPositionName(item.position) }}</span>
                <span class="reason">{{ item.reason }}</span>
              </div>
            </template>
            <div class="data-none" v-if="injuryList.findIndex(item => item.type === 2) === -1">
              无伤病信息
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 胜分差 -->
    <div class="data-section" v-if="gapHeaderData.home_allNum !== 0 || gapHeaderData.away_allNum !== 0">
      <div class="section-header">
        <div class="sub-header">
          <div class="red-line"></div>
          <span class="section-title">胜分差</span>
        </div>
        <div class="switch-buttons">
          <div class="switch-btn" :class="{ active: gapSwitchType === 0 }" @click="switchGapType(0)">胜
          </div>
          <div class="switch-btn" :class="{ active: gapSwitchType === 1 }" @click="switchGapType(1)">负
          </div>
        </div>
      </div>
      <div class="gap-section">
        <div class="team-header">
          <div>
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div>
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
        </div>
        <div class="gap-table">
          <div class="table-header">
            <span>主{{ gapHeaderData.away_homeNum }}</span>
            <span>客{{ gapHeaderData.away_awayNum }}</span>
            <span>总({{ gapHeaderData.away_allNum }})</span>
            <span>全场</span>
            <span>主({{ gapHeaderData.home_homeNum }})</span>
            <span>客({{ gapHeaderData.home_awayNum }})</span>
            <span>总({{ gapHeaderData.home_allNum }})</span>
          </div>
          <div class="table-body">
            <div class="table-row" v-for="(item, index) in allGapData" :key="index">
              <span :class="{ red: isMaxNumber(item.away.homeNum, allGapData, 'away.homeNum') }">{{
                item.away.homeNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.away.awayNum, allGapData, 'away.awayNum') }">{{ item.away.awayNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.away.allNum, allGapData, 'away.allNum') }">{{ item.away.allNum
              }}</span>
              <span>{{ item.gapText }}</span>
              <span :class="{ red: isMaxNumber(item.home.homeNum, allGapData, 'home.homeNum') }">{{ item.home.homeNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.home.awayNum, allGapData, 'home.awayNum') }">{{ item.home.awayNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.home.allNum, allGapData, 'home.allNum') }">{{ item.home.allNum
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 半全场胜负 -->
    <div class="data-section" v-if="halfHeaderData.home_allNum !== 0 || halfHeaderData.away_allNum !== 0">
      <div class="section-header">
        <div class="sub-header">
          <div class="red-line"></div>
          <span class="section-title">半全场胜负</span>
        </div>
        <div class="switch-buttons">
          <div class="switch-btn" :class="{ active: halfAllMatchCount === 10 }" @click="switchHalfCount(10)">10场
          </div>
          <div class="switch-btn" :class="{ active: halfAllMatchCount === 20 }" @click="switchHalfCount(20)">20场
          </div>
        </div>
      </div>
      <div class="half-all-section">
        <div class="team-header">
          <div>
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div>
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
        </div>
        <div class="half-all-table">
          <div class="table-header">
            <span>主({{ halfHeaderData.away_homeNum }})</span>
            <span>客({{ halfHeaderData.away_awayNum }})</span>
            <span>总({{ halfHeaderData.away_allNum }})</span>
            <span>全场</span>
            <span>主({{ halfHeaderData.home_homeNum }})</span>
            <span>客({{ halfHeaderData.home_awayNum }})</span>
            <span>总({{ halfHeaderData.home_allNum }}) </span>
          </div>
          <div class="table-body">
            <div class="table-row" v-for="(item, index) in halfAllStats" :key="index">
              <span :class="{ red: isMaxNumber(item.away.homeNum, halfAllStats, 'away.homeNum') }">{{ item.away.homeNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.away.awayNum, halfAllStats, 'away.awayNum') }">{{ item.away.awayNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.away.allNum, halfAllStats, 'away.allNum') }">{{ item.away.allNum
              }}</span>
              <span>{{ item.gapText }}</span>
              <span :class="{ red: isMaxNumber(item.home.homeNum, halfAllStats, 'home.homeNum') }">{{ item.home.homeNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.home.awayNum, halfAllStats, 'home.awayNum') }">{{ item.home.awayNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.home.allNum, halfAllStats, 'home.allNum') }">{{ item.home.allNum
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <noneBox v-if="homeRankingData?.matches == 0 && awayRankingData?.matches == 0 &&
    awaySwitchHistoryData.length == 0 && homeSwitchHistoryData.length == 0 &&
    !homeAverageData?.matches && !awayAverageData?.matches &&
    !historyStats?.totalMatches && !injuryList.length &&
    !gapHeaderData.home_allNum && halfHeaderData.home_allNum == 0" text="暂无数据"></noneBox>
</template>

<script setup lang="ts">
// 组件逻辑
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import { isEven, isMaxNumber } from '@/utils/common'
import defaultPlayerLogo from '@/static/index/default-player-logo.png'
import { getBasketHistoryApi, getInjuryApi, getBasketAvgApi, getBasketTableApi } from '@/api/match'

const props = defineProps({
  matchId: {
    type: Number,
    required: true
  },
  liveDetail: {
    type: Object,
    required: true,
    default: () => ({
      awayLogo: '',
      awayName: '',
      homeLogo: '',
      homeName: '',
      sclassName: '',
      homeId: 0,
      awayId: 0,
    })
  }
})

//篮球场均数据接口
interface BasketballAverageData {
  points: number;
  rebounds: number;
  assists: number;
  steals: number;
  matches: number;
  blocks: number;
  fieldGoalsAccuracy?: string;//投篮命中率
  threePointersAccuracy?: string;//三分
  freeThrowsAccuracy?: string;//罚球
}

// 篮球场均数据
const homeAverageData = ref<BasketballAverageData>({
  points: 0,
  rebounds: 0,
  assists: 0,
  matches: 0,
  steals: 0,
  blocks: 0,
  fieldGoalsAccuracy: '0%',//投篮命中率
  threePointersAccuracy: '0%',//三分
  freeThrowsAccuracy: '0%'//罚球
})

const awayAverageData = ref<BasketballAverageData>({
  points: 0,
  rebounds: 0,
  assists: 0,
  matches: 0,
  steals: 0,
  blocks: 0,
  fieldGoalsAccuracy: '0%',
  threePointersAccuracy: '0%',
  freeThrowsAccuracy: '0%'
})

// 历史交锋和近期战绩的场次切换
const historyMatchCount = ref(10) // 10场或20场


// 切换历史交锋场次
const switchHistoryCount = (count: number) => {
  historyMatchCount.value = count
  switchHistoryData.value = historyData.value.slice(0, count);
}
const switchHomeCount = (count: number) => {
  homeMatchCount.value = count
  homeSwitchHistoryData.value = homeHistoryData.value.slice(0, count);
}
const switchAwayCount = (count: number) => {
  awayMatchCount.value = count
  awaySwitchHistoryData.value = awayHistoryData.value.slice(0, count);
}
// 计算历史交锋统计数据
const calculateHistoryStats = (teamName: string, historyData: any[]) => {
  if (!teamName || !historyData || historyData.length === 0) {
    return {
      wins: 0,
      draws: 0,
      losses: 0,
      winRate: '0%',
      drawRate: '0%',
      lossRate: '0%',
      totalMatches: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      winHandicap: 0,
      loseHandicap: 0,
      drawHandicap: 0,
      handicapWinRate: '0%',
      bigBall: 0,
      smallBall: 0,
      drawBall: 0,
      bigBallRate: '0%',
      oddBallRate: '0%',
      evenBall: 0,
      oddBall: 0
    }
  }

  let wins = 0
  let draws = 0
  let losses = 0
  let goalsFor = 0
  let goalsAgainst = 0
  let winHandicap = 0
  let loseHandicap = 0
  let drawHandicap = 0
  let bigBall = 0
  let smallBall = 0
  let drawBall = 0
  let evenBall = 0
  let oddBall = 0

  historyData.forEach(match => {
    const isHome = match.homeName === teamName;
    const isAway = match.awayName === teamName;
    // 计算单双
    if (isEven((match.homeScore + match.homeOverScore) + (match.awayScore + match.awayOverScore))) {
      evenBall++
    } else {
      oddBall++
    }
    if (isHome || isAway) {
      const homeScore = parseInt(match.homeScore) + parseInt(match.homeOverScore) || 0
      const awayScore = parseInt(match.awayScore) + parseInt(match.awayOverScore) || 0

      // 计算进球失球
      if (isHome) {
        goalsFor += homeScore
        goalsAgainst += awayScore
      } else {
        goalsFor += awayScore
        goalsAgainst += homeScore
      }

      // 计算胜负平（基于比分）
      // if (homeScore === awayScore) {
      // draws++
      // } else 
      if ((isHome && homeScore > awayScore) || (isAway && awayScore > homeScore)) {
        wins++
      } else {
        losses++
      }
    }

    // 计算赢率（基于 asianStatus，不区分主客队）
    // asianStatus: 1-输, 2-赢, 3-平
    const asianStatus = parseInt(match.asianStatus) || 0
    if (asianStatus === 2) {
      winHandicap++
    } else if (asianStatus === 1) {
      loseHandicap++
    } else if (asianStatus === 3) {
      drawHandicap++
    }

    // 计算大率（基于 sizeStatus，不区分主客队）
    // sizeStatus: 1-小, 2-大, 3-平
    const sizeStatus = parseInt(match.sizeStatus) || 0
    if (sizeStatus === 2) {
      bigBall++
    } else if (sizeStatus === 1) {
      smallBall++
    } else if (sizeStatus === 3) {
      drawBall++
    }
  })

  const totalMatches = wins + draws + losses
  const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0
  // const drawRate = totalMatches > 0 ? Math.round((draws / totalMatches) * 100) : 0
  const lossRate = totalMatches > 0 ? Math.round((losses / totalMatches) * 100) : 0

  // 计算赢率（让球盘口）
  const totalHandicapMatches = winHandicap + loseHandicap + drawHandicap
  const handicapWinRate = totalHandicapMatches > 0 ? Math.round((winHandicap / totalMatches) * 100) : 0

  // 计算大率（大小球盘口）
  const totalSizeMatches = bigBall + smallBall + drawBall
  const bigBallRate = totalSizeMatches > 0 ? Math.round((bigBall / totalMatches) * 100) : 0

  return {
    wins,
    draws,
    losses,
    winRate: `${winRate}%`,
    // drawRate: `${drawRate}%`,
    lossRate: `${lossRate}%`,
    totalMatches,
    goalsFor,
    goalsAgainst,
    winHandicap,
    loseHandicap,
    drawHandicap,
    handicapWinRate: `${handicapWinRate}%`,
    bigBall,
    smallBall,
    drawBall,
    bigBallRate: `${bigBallRate}%`,
    oddBallRate: `${(oddBall / totalMatches * 100).toFixed(0)}%`,
    evenBall,
    oddBall
  }
}

// 计算历史交锋统计数据（响应式）
const historyStats = computed(() => {
  return calculateHistoryStats(props.liveDetail.homeName, switchHistoryData.value)
})

// 计算近期战绩统计数据（响应式）
const homeStats = computed(() => {
  return calculateHistoryStats(props.liveDetail.homeName, homeSwitchHistoryData.value)
})
const awayStats = computed(() => {
  return calculateHistoryStats(props.liveDetail.awayName, awaySwitchHistoryData.value)
})
//历史交锋
interface MatchFootHistoryDetailRes {
  /**
   * 联赛名称
   */
  aliasName?: string;
  /**
   * 让分
   */
  asianPlate?: string;
  /**
   * 让分走势 1.输 2.赢 3.平
   */
  asianStatus?: string;
  /**
   * 客队角球
   */
  awayCornerKick?: number;
  /**
   * 客队半场
   */
  awayHalfScore?: number;
  /**
   * 客队名称
   */
  awayName?: string;
  /**
   * 客队加时比分
   */
  awayOverScore: number;
  /**
   * 客队比分
   */
  awayScore: number;
  /**
   * 客队id
   */
  awayId?: number;
  /**
   * 主队角球
   */
  homeCornerKick?: number;
  /**
   * 主队半场
   */
  homeHalfScore?: number;
  /**
   * 主队名称
   */
  homeName?: string;
  /**
   * 主队加时比分
   */
  homeOverScore: number;
  /**
   * 主队比分
   */
  homeScore: number;
  /**
   * 主队id
   */
  homeId?: number;
  /**
   * 比赛id
   */
  id?: number;
  /**
   * 比赛状态
   */
  matchState?: number;
  /**
   * 比赛时间
   */
  matchTime?: string;
  /**
   * 总分
   */
  sizePlate?: string;
  /**
   * 总分走势 1.小 2.大 3.平
   */
  sizeStatus?: string;
  [property: string]: any;
}
const historyData = ref<MatchFootHistoryDetailRes[]>([])
const switchHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const getHistory = async () => {
  try {
    //0.历史交锋 1.主队 2.客队
    let res = await getBasketHistoryApi({ matchId: props.matchId, type: 0 })
    if (res.data && Array.isArray(res.data)) {
      historyData.value = res.data;
      switchHistoryData.value = res.data.slice(0, historyMatchCount.value);
    }
  } catch (error) {
    console.error('获取历史交锋数据失败:', error)
  }
}
//获取主队近期战绩
const homeHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const homeSwitchHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const homeMatchCount = ref(10) // 10场或20场
//计算分差数据
const homeGapData = ref<MatchFootHistoryDetailRes[]>([])
const getHomeHistory = async () => {
  try {
    //0.历史交锋 1.主队 2.客队
    let res = await getBasketHistoryApi({ matchId: props.matchId, type: 1 })
    if (res.data && Array.isArray(res.data)) {
      homeHistoryData.value = res.data;
      homeSwitchHistoryData.value = res.data.slice(0, homeMatchCount.value);
      homeHalfSwitchData.value = res.data.slice(0, halfAllMatchCount.value);
      homeGapData.value = res.data.slice(0, 20);
    }
  } catch (error) {
    console.error('获取主队历史数据失败:', error)
  }
}
//获取客队近期战绩
const awayHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const awaySwitchHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const awayMatchCount = ref(10) // 10场或20场
//计算分差数据
const awayGapData = ref<MatchFootHistoryDetailRes[]>([])
const getAwayHistory = async () => {
  try {
    //0.历史交锋 1.主队 2.客队
    let res = await getBasketHistoryApi({ matchId: props.matchId, type: 2 })
    if (res.data && Array.isArray(res.data)) {
      awayHistoryData.value = res.data;
      awaySwitchHistoryData.value = res.data.slice(0, awayMatchCount.value);
      awayHalfSwitchData.value = res.data.slice(0, halfAllMatchCount.value);
      awayGapData.value = res.data.slice(0, 20);
    }
  } catch (error) {
    console.error('获取客队历史数据失败:', error)
  }
}

//获取胜分差
let gapSwitchType = ref(0)//0胜 1负
// let gapHeaderData = reactive({
//   home_homeNum: 0,
//   home_awayNum: 0,
//   home_allNum: 0,
//   away_homeNum: 0,
//   away_awayNum: 0,
//   away_allNum: 0
// })
const gapHeaderData = computed(() => {
  const data = allGapData.value
  return {
    home_homeNum: data.reduce((acc, cur) => acc + cur.home.homeNum, 0),
    home_awayNum: data.reduce((acc, cur) => acc + cur.home.awayNum, 0),
    home_allNum: data.reduce((acc, cur) => acc + cur.home.allNum, 0),
    away_homeNum: data.reduce((acc, cur) => acc + cur.away.homeNum, 0),
    away_awayNum: data.reduce((acc, cur) => acc + cur.away.awayNum, 0),
    away_allNum: data.reduce((acc, cur) => acc + cur.away.allNum, 0)
  }
})
// 切换胜负类型
const switchGapType = (type: number) => {
  gapSwitchType.value = type
}

// 胜分差数据结构
let gapData = ref([
  {
    home: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    },
    gapText: '1-5',
    away: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    }
  },
  {
    home: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    },
    gapText: '6-10',
    away: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    }
  }, {
    home: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    },
    gapText: '11-15',
    away: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    }
  }, {
    home: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    },
    gapText: '16-20',
    away: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    }
  }, {
    home: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    },
    gapText: '21-25',
    away: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    }
  },
  {
    home: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    },
    gapText: '26+',
    away: {
      homeNum: 0,
      awayNum: 0,
      allNum: 0
    }
  }
])

// 根据分差获取对应的索引
const getGapIndex = (gap: number) => {
  if (gap >= 1 && gap <= 5) return 0
  if (gap >= 6 && gap <= 10) return 1
  if (gap >= 11 && gap <= 15) return 2
  if (gap >= 16 && gap <= 20) return 3
  if (gap >= 21 && gap <= 25) return 4
  if (gap >= 26) return 5
  return -1
}

// 计算胜分差统计
const calcGapStats = () => {
  // 创建新的数据副本，避免修改原始数据
  const newGapData = gapData.value.map(item => ({
    ...item,
    home: { homeNum: 0, awayNum: 0, allNum: 0 },
    away: { homeNum: 0, awayNum: 0, allNum: 0 }
  }))

  // 处理主队数据
  if (homeGapData.value.length > 0) {
    homeGapData.value.forEach((matchItem) => {
      const homeScore = (matchItem.homeScore + matchItem.homeOverScore) || 0
      const awayScore = (matchItem.awayScore + matchItem.awayOverScore) || 0
      const gap = Math.abs(homeScore - awayScore)
      const gapIndex = getGapIndex(gap)

      if (gapIndex === -1) return

      // 判断是主场还是客场
      const isHomeTeam = matchItem.homeId === props.liveDetail.homeId

      if (gapSwitchType.value === 0) {
        // 胜球统计
        if ((isHomeTeam && homeScore > awayScore) || (!isHomeTeam && awayScore > homeScore)) {
          if (isHomeTeam) {
            newGapData[gapIndex].home.homeNum++
          } else {
            newGapData[gapIndex].home.awayNum++
          }
          newGapData[gapIndex].home.allNum++
        }
      } else {
        // 负球统计
        if ((isHomeTeam && homeScore < awayScore) || (!isHomeTeam && awayScore < homeScore)) {
          if (isHomeTeam) {
            newGapData[gapIndex].home.homeNum++
          } else {
            newGapData[gapIndex].home.awayNum++
          }
          newGapData[gapIndex].home.allNum++
        }
      }
    })
  }

  // 处理客队数据
  if (awayGapData.value.length > 0) {
    awayGapData.value.forEach((matchItem) => {
      const homeScore = (matchItem.homeScore + matchItem.homeOverScore) || 0
      const awayScore = (matchItem.awayScore + matchItem.awayOverScore) || 0
      const gap = Math.abs(homeScore - awayScore)
      const gapIndex = getGapIndex(gap)

      if (gapIndex === -1) return

      // 判断是主场还是客场
      const isHomeTeam = matchItem.homeId === props.liveDetail.awayId

      if (gapSwitchType.value === 0) {
        // 胜球统计
        if ((isHomeTeam && homeScore > awayScore) || (!isHomeTeam && awayScore > homeScore)) {
          if (isHomeTeam) {
            newGapData[gapIndex].away.homeNum++
          } else {
            newGapData[gapIndex].away.awayNum++
          }
          newGapData[gapIndex].away.allNum++
        }
      } else {
        // 负球统计
        if ((isHomeTeam && homeScore < awayScore) || (!isHomeTeam && awayScore < homeScore)) {
          if (isHomeTeam) {
            newGapData[gapIndex].away.homeNum++
          } else {
            newGapData[gapIndex].away.awayNum++
          }
          newGapData[gapIndex].away.allNum++
        }
      }
    })
  }
  return newGapData
}

// 响应式计算胜分差数据
const allGapData = computed(() => {
  return calcGapStats()
})
//获取伤停
interface MatchFootInjuryDetailRes {
  id?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 球员logo
   */
  playerLogo?: string;
  /**
   * 球员名称
   */
  playerName?: string;
  /**
   * 球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知
   */
  position: string;
  /**
   * 伤停原因
   */
  reason?: string;
  /**
   * 球员号
   */
  shirtNumber?: string;
  /**
   * 球队id
   */
  teamId?: number;
  /**
   * 1主 2客
   */
  type?: number;
  [property: string]: any;
}
//获取半全场胜负
const halfAllMatchCount = ref(10) // 10场或20场
const awayHalfSwitchData = ref<MatchFootHistoryDetailRes[]>([])
const homeHalfSwitchData = ref<MatchFootHistoryDetailRes[]>([])
const halfHeaderData = computed(() => {
  const data = halfAllStats.value
  return {
    home_homeNum: data.reduce((acc, cur) => acc + cur.home.homeNum, 0),
    home_awayNum: data.reduce((acc, cur) => acc + cur.home.awayNum, 0),
    home_allNum: data.reduce((acc, cur) => acc + cur.home.allNum, 0),
    away_homeNum: data.reduce((acc, cur) => acc + cur.away.homeNum, 0),
    away_awayNum: data.reduce((acc, cur) => acc + cur.away.awayNum, 0),
    away_allNum: data.reduce((acc, cur) => acc + cur.away.allNum, 0)
  }
})
const switchHalfCount = (count: number) => {
  halfAllMatchCount.value = count
  awayHalfSwitchData.value = awayHistoryData.value.slice(0, count);
  homeHalfSwitchData.value = homeHistoryData.value.slice(0, count);
}
// 计算近期战绩统计数据（响应式）
const halfAllStats = computed(() => {
  return calcHalfAllStats()
})

const calcHalfAllStats = () => {

  let dataList = [
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '胜胜',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '胜负',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }, {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '平胜',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }, {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '平负',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }, {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '负胜',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '负负',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }
  ]
  if (homeHalfSwitchData.value.length > 0) {
    homeHalfSwitchData.value.forEach((matchItem) => {
      if (!matchItem.homeBqc || !Array.isArray(matchItem.homeBqc) || matchItem.homeBqc.length < 2) {
        return;
      }
      let guestBqc = matchItem.homeBqc;
      let homeBqc = guestBqc[0].split(',')
      let awayBqc = guestBqc[1].split(',')
      homeBqc.splice(1, 1)
      homeBqc.splice(3, 1)
      homeBqc.splice(5, 1)
      awayBqc.splice(1, 1)
      awayBqc.splice(3, 1)
      awayBqc.splice(5, 1)
      if (guestBqc[0]) {
        homeBqc.forEach((subItem: string, subind: number) => {
          if (dataList[subind]) {
            dataList[subind].home.homeNum += parseInt(subItem) || 0;
          }
        });
        awayBqc.forEach((subItem: string, subind: number) => {
          if (dataList[subind]) {
            dataList[subind].home.awayNum += parseInt(subItem) || 0;
          }
          dataList[subind].home.allNum = dataList[subind].home.awayNum + dataList[subind].home.homeNum;
        });
      }
    })
  }
  if (awayHalfSwitchData.value.length > 0) {
    awayHalfSwitchData.value.forEach((matchItem) => {
      if (!matchItem.guestBqc || !Array.isArray(matchItem.guestBqc) || matchItem.guestBqc.length < 2) {
        return;
      }
      let guestBqc = matchItem.guestBqc;
      let homeBqc = guestBqc[0].split(',')
      let awayBqc = guestBqc[1].split(',')
      homeBqc.splice(1, 1)
      homeBqc.splice(3, 1)
      homeBqc.splice(5, 1)
      awayBqc.splice(1, 1)
      awayBqc.splice(3, 1)
      awayBqc.splice(5, 1)
      if (guestBqc[0]) {
        homeBqc.forEach((subItem: string, subind: number) => {
          if (dataList[subind]) {
            dataList[subind].away.homeNum += parseInt(subItem) || 0;
          }
        });
        awayBqc.forEach((subItem: string, subind: number) => {
          if (dataList[subind]) {
            dataList[subind].away.awayNum += parseInt(subItem) || 0;
          }
          dataList[subind].away.allNum = dataList[subind].away.awayNum + dataList[subind].away.homeNum;
        });

      }
    })
  }
  return dataList;
}
let injuryList = ref<MatchFootInjuryDetailRes[]>([])
const getInjury = async () => {
  try {
    let res = await getInjuryApi({ matchId: props.matchId })
    if (res.data && Array.isArray(res.data)) {
      injuryList.value = res.data
    }
  } catch (error) {
    console.error('获取伤停数据失败:', error)
  }
}
const positionMap = {
  F: '前锋',
  M: '中场',
  D: '后卫',
  G: '守门员',
};
const getPositionName = (position: string) => {
  return positionMap[position] || '未知';
};
const sizeStatusMap = {
  1: '小',
  2: '大',
  3: '走'
};
const getSizePlateName = (sizeStatus: string | number | undefined) => {
  if (sizeStatus === undefined) return '-';
  return sizeStatusMap[sizeStatus] || '-';
};
const asianStatusMap = {
  1: '输',
  2: '赢',
  3: '走'
}
const getAsianStatusName = (asianStatus: string | number | undefined) => {
  if (asianStatus === undefined) return '-';
  return asianStatusMap[asianStatus] || '-';
};
//获取队伍场均数据
const getBasketAvg = async () => {
  try {
    let res = await getBasketAvgApi({ matchId: props.matchId })
    // 确保数据存在且有效，否则保持默认值
    if (res.data?.homeAvg && typeof res.data.homeAvg === 'object') {
      homeAverageData.value = { ...homeAverageData.value, ...res.data.homeAvg }
    }
    if (res.data?.awayAvg && typeof res.data.awayAvg === 'object') {
      awayAverageData.value = { ...awayAverageData.value, ...res.data.awayAvg }
    }
  } catch (error) {
    console.error('获取场均数据失败:', error)
  }
}

// 判断比分是否显示红色
const getScoreClass = (match: any) => {
  const homeScore = parseInt(match.homeScore) + parseInt(match.homeOverScore) || 0
  const awayScore = parseInt(match.awayScore) + parseInt(match.awayOverScore) || 0

  // 如果当前比赛的主队是 liveDetail.homeName，则主队比分大时显示红色
  if (match.homeName === props.liveDetail.homeName) {
    if (homeScore > awayScore) {
      return 'red'
    } else if (awayScore > homeScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }
  // 如果当前比赛的客队是 liveDetail.homeName，则客队比分大时显示红色
  else if (match.awayName === props.liveDetail.homeName) {
    // return awayScore > homeScore
    if (awayScore > homeScore) {
      return 'red'
    } else if (homeScore > awayScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }

  return false
};
const getScoreClassByTeam = (match: any, teamName: string) => {
  const homeScore = parseInt(match.homeScore) + parseInt(match.homeOverScore) || 0
  const awayScore = parseInt(match.awayScore) + parseInt(match.awayOverScore) || 0

  // 如果当前比赛的主队是 liveDetail.homeName，则主队比分大时显示红色
  if (match.homeName === teamName) {
    if (homeScore > awayScore) {
      return 'red'
    } else if (awayScore > homeScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }
  // 如果当前比赛的客队是 liveDetail.homeName，则客队比分大时显示红色
  else if (match.awayName === teamName) {
    // return awayScore > homeScore
    if (awayScore > homeScore) {
      return 'red'
    } else if (homeScore > awayScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }

  return false
};
//篮球积分排名数据接口
interface BasketballRankingData {
  position: number;
  win: number;
  total: number;
  matches: number;
  points: number;
  pointsAgainst: number;
}
//获取联赛积分排名
const homeRankingData = ref<BasketballRankingData>({
  position: 0,
  win: 0,
  total: 0,
  matches: 0,
  points: 0,
  pointsAgainst: 0,
})
const awayRankingData = ref<BasketballRankingData>({
  position: 0,
  win: 0,
  total: 0,
  matches: 0,
  pointsAgainst: 0,
  points: 0,
})

const getBasketTable = async () => {
  try {
    let res = await getBasketTableApi({ matchId: props.matchId })
    // 确保数据存在且有效，否则保持默认值
    if (res.data?.homeTable && typeof res.data.homeTable === 'object') {
      homeRankingData.value = { ...homeRankingData.value, ...res.data.homeTable }
    }
    if (res.data?.awayTable && typeof res.data.awayTable === 'object') {
      awayRankingData.value = { ...awayRankingData.value, ...res.data.awayTable }
    }
  } catch (error) {
    console.error('获取积分排名数据失败:', error)
  }
}
// 异步并行加载所有数据
const initData = async () => {
  try {
    await Promise.allSettled([
      getHistory(),
      getHomeHistory(),
      getAwayHistory(),
      getInjury(),
      getBasketAvg(),
      getBasketTable()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 页面加载时初始化数据
initData()
</script>

<style lang="scss" scoped>
.live-data-view {
  width: 100%;
  background: #fff;
  overflow-x: auto;

  .data-section {
    margin-bottom: 20px;
    padding: 16px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      // padding: 10px 6px;
      .sub-header {
        display: flex;
        align-items: center;
      }

      .red-line {
        width: 4px;
        height: 20px;
        background: #EB0000;
        margin-right: 10px;
      }

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .switch-buttons {
        display: flex;
        border-radius: 30px;
        background: #F5F5F5;
        margin-left: auto;

        .switch-btn {
          color: #666;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 30px;
          cursor: pointer;
          transition: all 0.2s;
          min-width: 40px;
          text-align: center;

          &:hover {
            background: #E8E8E8;
          }

          &.active {
            background: #EB0000;
            color: white;
            border-color: #EB0000;
          }
        }
      }
    }
  }

  // 积分排名样式
  .ranking-section {
    border-radius: 8px;
    padding: 15px;

    .ranking-table {
      display: flex;
      gap: 20px;

      .team-ranking {
        flex: 1;
        border-radius: 8px;

        .team-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          .team-logo {
            width: 24px;
            height: 24px;
            display: block;
            margin-right: 8px;
          }

          .team-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
        }

        .ranking-stats {
          .ranking-table-content {
            .ranking-header {
              display: grid;
              grid-template-columns: repeat(8, 1fr);
              gap: 8px;
              padding: 8px 0;
              background: #F5F5F5;
              font-weight: 600;
              color: #333;
              margin-bottom: 5px;
              border-radius: 4px;

              span {
                text-align: center;
                font-size: 12px;
              }
            }

            .ranking-row {
              display: grid;
              grid-template-columns: repeat(8, 1fr);
              gap: 8px;
              padding: 8px 0;
              border-bottom: 1px solid #F0F0F0;
              align-items: center;

              span {
                text-align: center;
                font-size: 12px;
                color: #333;

                &.red {
                  color: #EB0000;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
    }
  }

  // 场均数据样式
  .average-data-section {
    border-radius: 8px;
    padding: 15px;

    .average-data-table {
      display: flex;
      gap: 20px;

      .team-average {
        flex: 1;
        border-radius: 8px;

        .team-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          .team-logo {
            width: 24px;
            height: 24px;
            display: block;
            margin-right: 8px;
          }

          .team-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
        }

        .average-stats {
          .average-table-content {
            .average-header {
              display: grid;
              grid-template-columns: repeat(8, 1fr);
              padding: 8px 0;
              background: #F5F5F5;
              font-weight: 600;
              color: #333;
              margin-bottom: 5px;
              border-radius: 4px;

              span {
                text-align: center;
                font-size: 11px;
              }
            }

            .average-row {
              display: grid;
              grid-template-columns: repeat(8, 1fr);
              gap: 8px;
              padding: 8px 0;
              border-bottom: 1px solid #F0F0F0;
              align-items: center;

              span {
                text-align: center;
                font-size: 12px;
                color: #333;

                &.red {
                  color: #EB0000;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
    }
  }

  // 历史交锋样式
  .history-section {
    border-radius: 8px;
    padding: 15px;

    .history-header {
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
      gap: 15px;

      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .logo {
          display: flex;
          align-items: center;
        }
      }

      .team-logo {
        width: 24px;
        height: 24px;
        display: block;
        margin-right: 6px;
      }

      .team-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .stats {
        display: flex;
        align-items: center;
        gap: 26px;

        .stat-item {
          font-size: 14px;
          color: #333;
          text-align: center;

          .label {
            font-size: 12px;
          }

          .number {
            font-size: 16px;
            color: #FF2F2E;
          }
        }
      }

      .switch-buttons {
        display: flex;
        border-radius: 30px;
        background: #F5F5F5;
        // padding: 4px;

        .switch-btn {
          color: #666;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 30px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #E8E8E8;
          }

          &.active {
            background: #EB0000;
            color: white;
            border-color: #EB0000;
          }
        }
      }
    }

    .history-matches {
      min-width: 500px;

      .match-item {
        display: grid;
        grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr 0.6fr;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #F0F0F0;
        align-items: center;
        border-radius: 4px;

        &:first-child {
          background: #F5F5F5;
          font-weight: 600;
          color: #333;
        }

        &:last-child {
          border-bottom: none;
        }

        span {
          text-align: center;
          font-size: 12px;
          color: #333;

          &.red {
            color: #EB0000;
          }

          &.green {
            color: #18A058;
          }
        }

        .red {
          color: #EB0000;
        }

        .green {
          color: #18A058;
        }

        .blue {
          color: #3177FD;
        }
      }
    }
  }

  // 近期战绩样式
  .recent-records {
    display: flex;
    gap: 20px;

    .record-table {
      flex: 1;
      // border: 1px solid #E8E8E8;
      border-radius: 8px;
      padding: 15px;

      .history-header {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
        gap: 15px;

        .top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .logo {
            display: flex;
            align-items: center;
          }
        }

        .team-logo {
          width: 24px;
          height: 24px;
          display: block;
          margin-right: 6px;
        }

        .team-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .stats {
          display: flex;
          align-items: center;
          gap: 26px;

          .stat-item {
            font-size: 14px;
            color: #333;
            text-align: center;

            .label {
              font-size: 12px;
            }

            .number {
              font-size: 16px;
              color: #FF2F2E;
            }
          }
        }

        .switch-buttons {
          display: flex;
          border-radius: 30px;
          background: #F5F5F5;
          // padding: 4px;

          .switch-btn {
            color: #666;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #E8E8E8;
            }

            &.active {
              background: #EB0000;
              color: white;
              border-color: #EB0000;
            }
          }
        }
      }

      .record-matches {
        min-width: 500px;

        .match-header {
          display: grid;
          grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr 0.6fr;
          gap: 8px;
          padding: 8px 0;
          background: #F5F5F5;
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;

          span {
            text-align: center;
            font-size: 12px;
          }
        }

        .match-row {
          display: grid;
          grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr 0.6fr;
          gap: 8px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          span {
            text-align: center;
            font-size: 12px;
            color: #333;

            &.red {
              color: #EB0000;
            }

            &.green {
              color: #18A058;
            }
          }

          .red {
            color: #EB0000;
          }

          .green {
            color: #18A058;
          }

          .blue {
            color: #3177FD;
          }
        }
      }
    }
  }

  // 伤停情况样式
  .injury-section {
    display: flex;
    gap: 20px;

    .injury-table {
      flex: 1;
      border: 1px solid #E8E8E8;
      border-radius: 8px;
      padding: 15px;

      .injury-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .team-logo {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

        .team-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .injury-list {
        .injury-header-row {
          display: grid;
          grid-template-columns: 1fr 160px 160px;
          gap: 15px;
          padding: 8px 0;
          background: #F5F5F5;
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;

          span {
            text-align: center;
            font-size: 12px;

            &:first-child {
              text-align: left;
            }
          }
        }

        .injury-row {
          display: grid;
          grid-template-columns: 1fr 160px 160px;
          gap: 15px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          .player-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .player-avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
            }

            .player-name {
              font-size: 12px;
              color: #333;
            }
          }

          .position,
          .reason {
            text-align: center;
            font-size: 12px;
            color: #333;
          }
        }
      }

      .data-none {
        text-align: center;
        padding: 10px 0;
      }
    }
  }

  //半全场胜负
  .half-all-section {
    border-radius: 8px;
    padding: 15px;

    .team-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      div {
        display: flex;
        align-items: center;
        gap: 8px;

        .team-logo {
          width: 24px;
          height: 24px;
        }

        .team-name {

          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .half-all-table {
      min-width: 500px;

      .table-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);

        gap: 8px;
        padding: 8px 0;
        background: #F5F5F5;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;

        span {
          text-align: center;
          font-size: 12px;
        }
      }

      .table-body {
        .table-row {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          gap: 8px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          span {
            text-align: center;
            font-size: 12px;
            color: #333;

            &.red {
              color: #EB0000;
              font-weight: bold;
            }

          }
        }
      }
    }
  }

  //胜分差
  .gap-section {
    border-radius: 8px;
    padding: 15px;

    .team-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      div {
        display: flex;
        align-items: center;
        gap: 8px;

        .team-logo {
          width: 24px;
          height: 24px;
        }

        .team-name {

          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .gap-table {
      min-width: 500px;

      .table-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);

        gap: 8px;
        padding: 8px 0;
        background: #F5F5F5;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;

        span {
          text-align: center;
          font-size: 12px;
        }
      }

      .table-body {
        .table-row {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          gap: 8px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          span {
            text-align: center;
            font-size: 12px;
            color: #333;

            &.red {
              color: #EB0000;
              font-weight: bold;
            }

          }
        }
      }
    }
  }
}
</style>
