<template>
  <div>
    <!-- 登录弹窗 -->
    <LoginModal
      v-model:show="showLogin"
      @switch-to-register="switchToRegister"
    />
    
    <!-- 注册弹窗 -->
    <RegisterModal
      v-model:show="showRegister"
      @switch-to-login="switchToLogin"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import LoginModal from './LoginModal.vue'
import RegisterModal from './RegisterModal.vue'

interface Props {
  show: boolean
  type?: 'login' | 'register'
}

interface Emits {
  (e: 'update:show', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'login'
})

const emit = defineEmits<Emits>()

// 响应式数据
const showLogin = ref(false)
const showRegister = ref(false)

// 监听外部传入的显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    if (props.type === 'login') {
      showLogin.value = true
    } else {
      showRegister.value = true
    }
  } else {
    showLogin.value = false
    showRegister.value = false
  }
})

// 监听弹窗关闭状态
watch([showLogin, showRegister], ([login, register]) => {
  if (!login && !register) {
    emit('update:show', false)
  }
})

// 方法
const switchToRegister = () => {
  showLogin.value = false
  showRegister.value = true
}

const switchToLogin = () => {
  showRegister.value = false
  showLogin.value = true
}

// 暴露方法给父组件
defineExpose({
  showLogin: () => {
    showLogin.value = true
    showRegister.value = false
  },
  showRegister: () => {
    showLogin.value = false
    showRegister.value = true
  },
  close: () => {
    showLogin.value = false
    showRegister.value = false
  }
})
</script>
