import request from '@/request/request'
// 获取ai好友类别
export const getRobotTypeApi = () => {
  return request.post<any, any>('/robotCate')
}
interface RobotListType {
  page: number
  limit: number
  type: number
}
// 通过类别获取好友列表
export const getRobotListApi = (data: RobotListType) => {
  return request.post<any, any>('/robotList', data)
}

// 添加ai好友
interface addRobotType {
  robot_id: number
}
export const addRobotFriendApi = (data: addRobotType) => {
  return request.post<any, any>('/addFriend', data)
}

// 添加试聊ai好友
export const addTestRobotFriendApi = (data: addRobotType) => {
  return request.post<any, any>('/addTestFriend', data)
}

//获取已添加Ai好友
interface FriendListType {
  friend_id: number
}
export const getFriendListApi = (data: FriendListType) => {
  return request.post<any, any>('/friendList', data)
}

//发送聊天
interface sendMsgType {
  friend_id: number
  type: number
  question: string
  chatgtp_type: number
}
export const sendMsgApi = (data: sendMsgType) => {
  return request.post<any, any>('/chatAi', data)
}

//保存聊天
interface saveMsgType {
  order_no: string
  reply: string
}
export const saveMsgApi = (data: saveMsgType) => {
  return request.post<any, any>('/chatAiNotice', data)
}

//好友详情
interface getFriendDetailType {
  friend_id: any
}
export const getFriendDetailApi = (data: getFriendDetailType) => {
  return request.post<any, any>('/testFriend', data)
}

//获取聊天详情
interface getMsgHistoryType {
  page: number
  limit: number
  friend_id: number
}
export const getMsgHistoryApi = (data: getMsgHistoryType) => {
  return request.post<any, any>('/chatList', data)
}

//删除聊天记录
interface deleteHistoryType {
  friend_id: number
}
export const deleteMsgHistoryApi = (data: deleteHistoryType) => {
  return request.post<any, any>('/delRecord', data)
}
