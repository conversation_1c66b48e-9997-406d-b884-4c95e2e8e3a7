<template>
  <div class="user-feedback">
    <div class="page-header">
      <div class="line"></div>
      <h3>我的反馈</h3>
    </div>
    <div class="page-content">
      <!-- 问题类型 -->
      <div class="form-section">
        <div class="section-label"><i>*</i>问题类型</div>
        <div class="problem-types">
          <div class="type-tag" :class="{ active: selectedType === type.value }" v-for="type in problemTypes"
            :key="type.value" @click="selectType(type.value)">
            {{ type.label }}
          </div>
        </div>
      </div>
      <!-- 补充说明 -->
      <div class="form-section">
        <div class="section-label"><i>*</i>补充说明</div>
        <textarea v-model="description" class="description-textarea" placeholder="请对您遇到的问题进行描述"
          maxlength="200"></textarea>
        <div class="char-count">({{ description.length }}/200)</div>
      </div>
      <!-- 图片上传 -->
      <div class="form-section">
        <div class="section-label">请提供相关问题的截图或图片 ({{ fileList.length }}/3)</div>
        <div class="image-upload-area">
          <n-upload v-model:file-list="fileList" show-remove-button list-type="image-card" :max="3" accept="image/*"
            @before-upload="handleBeforeUpload" />
        </div>
      </div>
      <!-- 联系方式 -->
      <div class="form-section">
        <div class="section-label"><i>*</i>请输入您的联系方式，以便我们与您联系：</div>
        <input v-model="contact" type="text" class="contact-input" placeholder="请输入联系方式">
      </div>
      <!-- 提交按钮 -->
      <div class="submit-section">
        <button class="submit-btn" @click="submitFeedback">
          提交反馈
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NUpload, type UploadFileInfo } from 'naive-ui'
import { useMessage } from 'naive-ui'
import { uploadFilePathFunH5 } from '@/common/upload'
import { feedbackApi } from '@/api/user'

interface ProblemType {
  label: string
  value: string
}

const problemTypes: ProblemType[] = [
  { label: '建议/举报', value: 'suggestion' },
  { label: '问题/Bug', value: 'bug' },
  { label: '产品活动资讯', value: 'activity' },
  { label: '充值问题', value: 'payment' },
  { label: '封禁问题', value: 'ban' },
  { label: '其他', value: 'other' }
]

const message = useMessage()
const selectedType = ref<string>('')
const description = ref<string>('')
const contact = ref<string>('')
const fileList = ref<UploadFileInfo[]>([])

const selectType = (type: string) => {
  selectedType.value = type
}

// 上传图片
const handleBeforeUpload = async (options: { file: UploadFileInfo, fileList: Array<UploadFileInfo>, event?: Event }) => {
  const files = Array.isArray(options.file) ? options.file : [options.file]
  // 使用 Promise.all 正确处理多个异步上传
  const uploadPromises = files.map(async (item) => {
    item.status = 'uploading'
    item.message = '上传中...'
    try {
      const formData = new FormData()
      formData.append('file', item.file)
      const { data: result } = await uploadFilePathFunH5(formData)
      // 更新文件状态
      item.status = 'finished'
      item.message = ''
      item.url = result.data
      return item
    } catch (error) {
      item.status = 'error'
      item.message = '上传失败'
      return item
    }
  })
  // 等待所有上传完成
  await Promise.all(uploadPromises)
  fileList.value = options.fileList
}

// 提交反馈
const submitFeedback = async () => {
  if (selectedType.value === '') {
    return message.warning('请选择问题类型')
  }
  if (description.value.trim() === '') {
    return message.warning('请输入补充说明')
  }
  // if (fileList.value.length === 0) {
  //   return message.warning('请上传图片')
  // }
  if (contact.value.trim() === '') {
    return message.warning('请输入联系方式')
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(contact.value.trim())) {
    return message.warning('请输入正确的联系方式')
  }
  const urls = fileList.value.map((item: any) => {
    if (item.url && typeof item.url === 'object' && item.url.data) {
      return item.url.data
    }
    return item.url
  })
  await feedbackApi({
    contentType: selectedType.value,
    content: description.value,
    contactWay: contact.value,
    contentImages: urls
  })
  window.$message.success('反馈提交成功')
  // 清空表单
  selectedType.value = ''
  description.value = ''
  contact.value = ''
  fileList.value = []
}
</script>

<style lang="scss" scoped>
.user-feedback {
  background-color: #fff;
  min-height: 100%;

  .page-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .page-content {
    padding: 0 36px 40px;

    .form-section {
      margin-bottom: 32px;

      .section-label {
        font-size: 14px;
        color: #333;
        margin-bottom: 16px;
        font-weight: 500;

        i {
          color: #FB2B1F;
        }
      }

      .problem-types {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .type-tag {
          padding: 4px 16px;
          border: 1px solid #E5E5E5;
          border-radius: 4px;
          font-size: 14px;
          color: #666;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #fff;

          &:hover {
            border-color: #FB2B1F;
            color: #FB2B1F;
          }

          &.active {
            background: #FB2B1F;
            border-color: #FB2B1F;
            color: #fff;
          }
        }
      }

      .description-textarea {
        width: 100%;
        min-height: 120px;
        padding: 12px;
        border: 1px solid #E5E5E5;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        resize: vertical;
        outline: none;
        box-sizing: border-box;

        &:focus {
          border-color: #FB2B1F;
        }

        &::placeholder {
          color: #999;
        }
      }

      .char-count {
        text-align: right;
        font-size: 12px;
        color: #999;
        margin-top: 8px;
      }

      .contact-input {
        width: 100%;
        padding: 12px;
        border: 1px solid #E5E5E5;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        outline: none;
        box-sizing: border-box;

        &:focus {
          border-color: #FB2B1F;
        }

        &::placeholder {
          color: #999;
        }
      }
    }

    .submit-section {
      text-align: center;
      margin-top: 40px;

      .submit-btn {
        width: 100%;
        max-width: 400px;
        padding: 12px 24px;
        background: #FB2B1F;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #E02419;
        }
      }
    }
  }
}
</style>
