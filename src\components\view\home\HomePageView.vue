<template>
  <div class="home-page">
    <div class="live-container">
      <div class="live-content">
        <div class="video-box">
          <div id="dplayer"></div>
          <div v-if="isReconnecting" class="reconnecting-overlay">
            <div class="reconnecting-message">
              <div class="loading-spinner"></div>
              <!-- <p>连接中断，正在重新连接...</p> -->
            </div>
          </div>
        </div>
        <div class="room-list">
          <ul>
            <li v-for="(item, index) in liveList" :key="item.id" @click="onSwitchLive(index)">
              <div class="room" :class="{ active: index === liveCurrent }">
                <i class="left-arrow"></i>
                <img class="cover" src="@/static/img/room.jpg">
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="home-container">

    </div>
  </div>
</template>

<script setup lang="ts">
import DPlayer from 'dplayer'
import Hls from 'hls.js'

// 播放器实例和 HLS 实例的引用
let dp: any = null;
let hlsInstance: any = null;
let heartbeatTimer: any = null;

// 响应式状态
const isReconnecting = ref(false);

// 心跳检测
const startHeartbeat = () => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer);
  }

  heartbeatTimer = setInterval(() => {
    if (dp && dp.video) {
      const video = dp.video;
      // const currentTime = video.currentTime;
      const buffered = video.buffered;

      // 检查是否有缓冲数据
      if (buffered.length === 0 && !video.paused && !video.ended) {
        console.log('检测到播放停滞，尝试重新连接...');
        isReconnecting.value = true;
        setTimeout(() => {
          if (hlsInstance) {
            hlsInstance.startLoad();
          }
          setTimeout(() => {
            isReconnecting.value = false;
          }, 3000);
        }, 1000);
      }
    }
  }, 10000); // 每10秒检查一次
};

const stopHeartbeat = () => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer);
    heartbeatTimer = null;
  }
};

// 使用 DPlayer 原生 switchVideo API 切换视频源
const switchVideo = (newUrl: string) => {
  if (!dp) {
    console.log('播放器未初始化，使用 initPlayer');
    initPlayer();
    return;
  }

  console.log('切换视频源到:', newUrl);
  // isReconnecting.value = true;

  try {
    // 使用 DPlayer 原生 API 切换视频
    dp.switchVideo(
      {
        url: newUrl,
        type: 'customHls',
        customType: {
          customHls: function (video: HTMLVideoElement, _player: any) {
            if (Hls.isSupported()) {
              // 清理之前的 HLS 实例
              if (hlsInstance) {
                hlsInstance.destroy();
              }

              hlsInstance = new Hls({
                enableWorker: false,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                maxMaxBufferLength: 600,
                maxBufferSize: 60 * 1000 * 1000,
                maxBufferHole: 0.5,
                highBufferWatchdogPeriod: 2,
                nudgeOffset: 0.1,
                nudgeMaxRetry: 3,
                maxFragLookUpTolerance: 0.25,
                liveSyncDurationCount: 3,
                liveMaxLatencyDurationCount: Infinity,
                liveDurationInfinity: false,
                enableSoftwareAES: true,
                manifestLoadingTimeOut: 10000,
                manifestLoadingMaxRetry: 1,
                manifestLoadingRetryDelay: 1000,
                levelLoadingTimeOut: 10000,
                levelLoadingMaxRetry: 4,
                levelLoadingRetryDelay: 1000,
                fragLoadingTimeOut: 20000,
                fragLoadingMaxRetry: 6,
                fragLoadingRetryDelay: 1000,
                startFragPrefetch: false,
                fpsDroppedMonitoringPeriod: 5000,
                fpsDroppedMonitoringThreshold: 0.2,
                appendErrorMaxRetry: 3,
                debug: false
              });

              hlsInstance.loadSource(video.src);
              hlsInstance.attachMedia(video);

              // 重新绑定错误处理
              hlsInstance.on(Hls.Events.ERROR, function (_event: any, data: any) {
                console.error('HLS Error:', data);
                if (data.fatal) {
                  isReconnecting.value = true;
                  switch (data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                      console.log('网络错误，尝试恢复...');
                      setTimeout(() => {
                        if (hlsInstance) {
                          hlsInstance.startLoad();
                          setTimeout(() => {
                            isReconnecting.value = false;
                          }, 2000);
                        }
                      }, 1000);
                      break;
                    case Hls.ErrorTypes.MEDIA_ERROR:
                      console.log('媒体错误，尝试恢复...');
                      setTimeout(() => {
                        if (hlsInstance) {
                          hlsInstance.recoverMediaError();
                          setTimeout(() => {
                            isReconnecting.value = false;
                          }, 2000);
                        }
                      }, 1000);
                      break;
                    default:
                      console.log('严重错误，重新初始化播放器...');
                      setTimeout(() => {
                        initPlayer();
                        isReconnecting.value = false;
                      }, 3000);
                      break;
                  }
                }
              });

            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
              // Safari 原生支持
              video.src = video.src;
            }
          },
        }
      },
    );
    dp.play();
    // 重置重连状态
    // setTimeout(() => {
    //   isReconnecting.value = false;
    // }, 2000);

  } catch (error) {
    console.error('切换视频失败:', error);
    // 如果切换失败，重新初始化播放器
    setTimeout(() => {
      initPlayer();
      isReconnecting.value = false;
    }, 1000);
  }
};



// 初始化播放器
const initPlayer = () => {
  const container = document.getElementById('dplayer');
  if (!container) return;

  // 清理之前的实例
  if (dp) {
    dp.destroy();
    dp = null;
  }
  if (hlsInstance) {
    hlsInstance.destroy();
    hlsInstance = null;
  }

  dp = new DPlayer({
    container: container,
    live: true,
    autoplay: true,
    theme: '#FADFA3',
    loop: false,
    lang: 'zh-cn',
    hotkey: true,
    preload: 'none',
    volume: 0,
    mutex: true,
    muted: true,
    video: {
      // 使用当前选中的直播间 URL
      url: liveList.value[liveCurrent.value]?.url,
      type: 'customHls',
      customType: {
        customHls: function (video: HTMLVideoElement, _player: any) {
          if (Hls.isSupported()) {
            hlsInstance = new Hls({
              enableWorker: false,
              lowLatencyMode: true,
              backBufferLength: 90,
              maxBufferLength: 30,
              maxMaxBufferLength: 600,
              maxBufferSize: 60 * 1000 * 1000,
              maxBufferHole: 0.5,
              highBufferWatchdogPeriod: 2,
              nudgeOffset: 0.1,
              nudgeMaxRetry: 3,
              maxFragLookUpTolerance: 0.25,
              liveSyncDurationCount: 3,
              liveMaxLatencyDurationCount: Infinity,
              liveDurationInfinity: false,
              enableSoftwareAES: true,
              manifestLoadingTimeOut: 10000,
              manifestLoadingMaxRetry: 1,
              manifestLoadingRetryDelay: 1000,
              levelLoadingTimeOut: 10000,
              levelLoadingMaxRetry: 4,
              levelLoadingRetryDelay: 1000,
              fragLoadingTimeOut: 20000,
              fragLoadingMaxRetry: 6,
              fragLoadingRetryDelay: 1000,
              startFragPrefetch: false,
              fpsDroppedMonitoringPeriod: 5000,
              fpsDroppedMonitoringThreshold: 0.2,
              appendErrorMaxRetry: 3,
              debug: false
            });

            hlsInstance.loadSource(video.src);
            hlsInstance.attachMedia(video);

            // 监听 HLS 事件
            hlsInstance.on(Hls.Events.MANIFEST_PARSED, function () {
              console.log('HLS manifest parsed');
              video.play()
            });

            hlsInstance.on(Hls.Events.LEVEL_LOADED, function () {
              console.log('HLS level loaded');
            });

            // 错误处理和自动恢复
            hlsInstance.on(Hls.Events.ERROR, function (_event: any, data: any) {
              console.error('HLS Error:', data);

              if (data.fatal) {
                isReconnecting.value = true;
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    console.log('网络错误，尝试恢复...');
                    setTimeout(() => {
                      if (hlsInstance) {
                        hlsInstance.startLoad();
                        setTimeout(() => {
                          isReconnecting.value = false;
                        }, 2000);
                      }
                    }, 1000);
                    break;
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    console.log('媒体错误，尝试恢复...');
                    setTimeout(() => {
                      if (hlsInstance) {
                        hlsInstance.recoverMediaError();
                        setTimeout(() => {
                          isReconnecting.value = false;
                        }, 2000);
                      }
                    }, 1000);
                    break;
                  default:
                    console.log('严重错误，重新初始化播放器...');
                    setTimeout(() => {
                      initPlayer();
                      isReconnecting.value = false;
                    }, 3000);
                    break;
                }
              }
            });

            // 监听播放器错误
            video.addEventListener('error', function (e) {
              console.error('Video error:', e);
              setTimeout(() => {
                initPlayer();
              }, 3000);
            });

            // 监听播放器暂停/播放状态
            video.addEventListener('pause', function () {
              console.log('Video paused');
            });

            video.addEventListener('play', function () {
              console.log('Video playing');
            });

          } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari 原生支持
            video.src = video.src;
          }
        },
      },
    },
  });

  // 监听播放器事件
  dp.on('error', function (info: any) {
    console.error('DPlayer Error:', info);
    setTimeout(() => {
      initPlayer();
    }, 3000);
  });

  dp.on('ended', function () {
    console.log('播放结束');
  });
  dp.on('canplay', () => {
    dp.muted = true;
    // dp.play().catch(e => console.log('自动播放被阻止:', e));
    document.addEventListener('click', () => {
      dp.play();
    }, { once: true });
  });

  // 启动心跳检测
  startHeartbeat();
};
const liveList = ref([
  {
    id: 1,
    name: '直播间1',
    url: 'http://192.168.77.217/rrty/sd-1-4322534_lgzm.m3u8?txSecret=2c453b340df4c83e2eec567d4a7fb420&txTime=6853F940'
  },
  {
    id: 2,
    name: '直播间2',
    url: 'http://192.168.77.217/rrty/sd-1-4322532.m3u8?txSecret=d92657a83a634336cc60df2510d03ad1&txTime=68527E6E'
  },
  {
    id: 3,
    name: '直播间3',
    url: 'http://192.168.77.217/rrty/sd-1-4322532_bqzm.m3u8?txSecret=59f8c74af0c78e0f1371d3d1a82e798e&txTime=6852D3B5'
  },
  {
    id: 4,
    name: '直播间4',
    url: 'http://192.168.77.217/rrty/sd-1-4322532_lgzm.m3u8?txSecret=3ec459817c8e055e8686c9e057ab44ce&txTime=6852D3B5'
  },
  {
    id: 5,
    name: '5',
    url: 'http://192.168.77.217/rrty/sd-1-4322532.m3u8?txSecret=581a49a47979ae6360a96b5a1634573c&txTime=68527F0E'
  }
])
const liveCurrent = ref(0)

// 优化的房间切换函数 - 使用 DPlayer 原生 switchVideo API
const onSwitchLive = (index: number) => {
  if (index === liveCurrent.value) {
    console.log('已经是当前直播间');
    return;
  }

  const targetRoom = liveList.value[index];
  if (!targetRoom) {
    console.error('直播间不存在');
    return;
  }

  console.log('切换到直播间:', targetRoom.name);
  liveCurrent.value = index;

  // 使用 DPlayer 原生 switchVideo API 而不是 initPlayer 来提高性能
  if (dp) {
    switchVideo(targetRoom.url);
  } else {
    // 如果播放器未初始化，则初始化播放器
    initPlayer();
  }
}
onMounted(() => {
  // 延迟初始化，确保 DOM 已经渲染
  nextTick(() => {
    initPlayer();
  });
});

// 组件卸载时清理资源
onUnmounted(() => {
  stopHeartbeat();
  if (hlsInstance) {
    hlsInstance.destroy();
    hlsInstance = null;
  }
  if (dp) {
    dp.destroy();
    dp = null;
  }
});
</script>

<style lang="scss" scoped>
.home-page {
  width: 100%;
  min-height: 100vh;
  background: #F3F3F3;

  .live-container {
    height: 650px;
    background-image: url('@/static/img/bg.jpg');
    background-position: center;
    background-size: cover;

    .live-content {
      width: 1200px;
      height: 650px;
      margin: 0 auto;
      display: flex;

      .video-box {
        width: 1015px;
        height: 650px;
        position: relative;

        #dplayer {
          width: 100%;
          height: 100%;
        }

        .reconnecting-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;

          .reconnecting-message {
            text-align: center;
            color: white;

            .loading-spinner {
              width: 40px;
              height: 40px;
              border: 4px solid #f3f3f3;
              border-top: 4px solid #ffc71c;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin: 0 auto 15px;
            }

            p {
              font-size: 16px;
              margin: 0;
            }
          }
        }
      }

      .room-list {
        width: 185px;
        display: flex;
        flex-direction: column;
        padding-left: 10px;
        background-color: #000;

        li {
          padding: 0 0 7px 0;
          margin-top: 10px;

          .room {
            display: block;
            background: rgba(0, 0, 0, .7);
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0);
            position: relative;
            width: 100%;
            height: 105px;
            border-radius: 6px;
            // overflow: hidden;

            .left-arrow {
              width: 13px;
              height: 11px;
              position: absolute;
              left: -15px;
              top: 50%;
              transform: translateY(-50%);
              display: none;

              &::after {
                position: absolute;
                content: '';
                border-top: 6px transparent dashed;
                border-left: 7px transparent dashed;
                border-bottom: 6px transparent dashed;
                border-right: 7px #FB2B1F solid;
              }
            }

            .cover {
              width: 100%;
              height: 100%;
              border-radius: 6px;
            }

            &:hover {
              border: 2px solid #FB2B1F;

              .left-arrow {
                display: block;
              }
            }

            &.active {
              border: 2px solid #FB2B1F;

              .left-arrow {
                display: block;
              }
            }
          }
        }
      }
    }
  }

  .home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;

    h1 {
      font-size: var(--font-size-650);
      color: var(--color-gray-800);
      margin-bottom: 20px;
    }

    p {
      font-size: var(--font-size-150);
      color: var(--color-gray-600);
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
