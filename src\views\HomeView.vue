<script setup lang="ts">
import SideNavView from '@/components/layout/SideNavView.vue'
import HeaderView from '@/components/layout/HeaderView.vue'
import FooterView from '@/components/layout/FooterView.vue'
</script>

<template>
  <HeaderView></HeaderView>
  <div class="body-container">
    <RouterView v-slot="{ Component }">
      <Transition name="fade-transform" mode="out-in" appear>
        <!-- <keep-alive> -->
        <component :is="Component"></component>
        <!-- </keep-alive> -->
      </Transition>
    </RouterView>
    <SideNavView />
  </div>
  <FooterView></FooterView>
</template>
<style lang="scss" scoped>
// .body-container {}</style>
