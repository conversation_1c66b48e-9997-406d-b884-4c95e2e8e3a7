import { defineStore } from 'pinia'
import { loginOutApi, getBaseUserInfoApi, insertTicket<PERSON>pi, getConfigLevelApi, getConfigTodoListApi, getTaskTodoListApi, insertExpApi } from '@/api/user'
interface ConfigLevel {
  createTime?: string;
  /**
   * 当前等级下限
   */
  expLow?: number;
  /**
   * 当前等级上限
   */
  expUp?: number;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 等级
   */
  level?: number;
  [property: string]: any;
}
enum ExpConfigEnum {
  DailyLogin = "dailyLogin",
  ShareLiveRoom = "shareLiveRoom",
}
enum TicketConfigEnum {
  DailyBarrage = "dailyBarrage",
  DailyFollow = "dailyFollow",
  FirstLogin = "firstLogin",
  InviteFriends = "InviteFriends",
  RewardAnchor = "rewardAnchor",
  SignInFive = "signInFive",
  SignInFour = "signInFour",
  SignInOne = "signInOne",
  SignInSeven = "signInSeven",
  SignInSix = "signInSix",
  SignInThree = "signInThree",
  SignInTwo = "signInTwo",
  ViewingDuration = "viewingDuration",
  signIn = "signIn"
}
interface configTodoList {
  endTime?: number;
  /**
   * 任务类型
   */
  expConfigEnum?: ExpConfigEnum;
  /**
   * 1完成 2未完成
   */
  type?: number;
  [property: string]: any;
}

interface taskTodoList {
  /**
   * 类型
   */
  transType?: TicketConfigEnum;
  /**
   * 1完成 2未完成
   */
  type?: number;
  [property: string]: any;
}
export const useTaskStore = defineStore('taskStore', {

  state: () => ({
    configLevelList: [] as ConfigLevel[],
    configTodoList: [] as configTodoList[],
    taskTodoList: [] as taskTodoList[],
    isDailyFollow: false,
    isDailyBarrage: false,
    isViewingDuration: false,
    isShareLiveRoom: false
  }),

  actions: {
    async getConfigLevel() {
      try {
        const res: any = await getConfigLevelApi()
        this.configLevelList = res.data
      } catch (error) {
        console.error('获取经验等级配置失败:', error)
      }
    },
    //查询用户每日经验任务完成情况
    async getConfigTodoList() {
      let res = await getConfigTodoListApi()
      this.configTodoList = res.data
      let dailyLogin = this.configTodoList.find((item) => item.expConfigEnum === ExpConfigEnum.DailyLogin)
      if (dailyLogin?.type === 2) {
        await insertExpApi({ expConfigEnum: ExpConfigEnum.DailyLogin })
        window.$message.success('每日登录奖励已领取')
      }
    },
    //查询用户每日球票任务完成情况
    async getTaskTodoList() {
      let res = await getTaskTodoListApi()
      this.taskTodoList = res.data;
      let firstLogin = this.taskTodoList.find((item) => item.transType === TicketConfigEnum.FirstLogin)
      if (firstLogin?.type === 2) {
        await insertTicketApi({ ticketConfigEnum: TicketConfigEnum.FirstLogin })
        window.$message.success('首登奖励已领取')
      }
    },
    //每日关注
    async dailyFollow() {
      if (this.isDailyFollow) {
        return
      }
      let dailyFollow = this.taskTodoList.find((item) => item.transType === TicketConfigEnum.DailyFollow)
      if (dailyFollow?.type === 2) {
        await insertTicketApi({ ticketConfigEnum: TicketConfigEnum.DailyFollow })
        window.$message.success('每日关注奖励已领取')
      }
      this.isDailyFollow = true;
    },
    //每日发弹幕
    async dailyBarrage() {
      if (this.isDailyBarrage) {
        return
      }
      let dailyBarrage = this.taskTodoList.find((item) => item.transType === TicketConfigEnum.DailyBarrage)
      if (dailyBarrage?.type === 2) {
        await insertTicketApi({ ticketConfigEnum: TicketConfigEnum.DailyBarrage })
        window.$message.success('每日发弹幕奖励已领取')
      }
      this.isDailyBarrage = true;

    },
    //观看时长到达5分钟
    async viewingDuration() {
      if (this.isViewingDuration) {
        return
      }
      let viewingDuration = this.taskTodoList.find((item) => item.transType === TicketConfigEnum.ViewingDuration)
      if (viewingDuration?.type === 2) {
        await insertTicketApi({ ticketConfigEnum: TicketConfigEnum.ViewingDuration })
        window.$message.success('观看时长到达5分钟奖励已领取')
      }
      this.isViewingDuration = true;
    },
    //每日分享
    async shareLiveRoom() {
      if (this.isShareLiveRoom) {
        return
      }
      let shareLiveRoom = this.configTodoList.find((item) => item.expConfigEnum === ExpConfigEnum.ShareLiveRoom)
      if (shareLiveRoom?.type === 2) {
        await insertExpApi({ expConfigEnum: ExpConfigEnum.ShareLiveRoom })
        window.$message.success('每日分享奖励已领取')
      }
      this.isShareLiveRoom = true;
    }
  }
})