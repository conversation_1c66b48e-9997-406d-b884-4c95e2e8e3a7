<template>
  <div class="details-icons-box" :style="[{ width: width + 'px', height: height + 'px' }]">
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'GOAL' || typeId === 1" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'SHOOT-ON' || typeId === 21" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'SHOOT-OFF' || typeId === 22" />
    <img class="icon" src="@/static/details/<EMAIL>"
      v-if="type === 'PENALTY-KICK' || typeId === 8 || typeId === 29" />
    <img class="icon" src="@/static/details/<EMAIL>"
      v-if="type === 'PENALTY-MISS' || typeId === 16 || typeId === 30" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'OWN-GOAL' || typeId === 17" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'SUBSTITUTION' || typeId === 9" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'TWO-YELLOW' || typeId === 15" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'YELLOW' || typeId === 3" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'RED' || typeId === 4" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'ASSIST' || typeId === 18" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'VAR' || typeId === 28" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'CORNER' || typeId === 2" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'LIVE-YEELOW'" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'LIVE-WHISTLE' || typeId === 10" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'LIVE-NOTIF'" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'SUB-UP'" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'SUB-DOWN'" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'CAPTAIN'" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'MVP'" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'END' || typeId === 12" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'GOALKICK' || typeId === 7" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'OFFSIDE' || typeId === 5" />
    <img class="icon" src="@/static/details/<EMAIL>" v-if="type === 'TIMEOUT' || typeId === 19" />
  </div>
</template>

<script setup>
/**
 * GOAL 进球
 * SHOOT-ON 射正
 * SHOOT-OFF 射偏
 * PENALTY-KICK 点球
 * PENALTY-MISS 点球未进
 * OWN-GOAL 乌龙球
 * SUBSTITUTION 换人
 * TWO-YELLOW 两黄一红
 * YELLOW 黄牌
 * RED 红牌
 * ASSIST 助攻
 * VAR var
 * CORNER 角球
 * LIVE-YEELOW 直播黄牌
 * LIVE-WHISTLE 直播吹哨
 * LIVE-NOTIF 直播通知
 * SUB-UP 换上
 * SUB-DOWN 换下
 * CAPTAIN 队长
 * MVP mvp
 * END 比赛结束
 * GOALKICK 球门球
 * OFFSIDE 越位
 * TIMEOUT 伤停补时
 */
const props = defineProps({
  type: {
    type: String,
    default: ''
  },
  typeId: {
    type: Number,
    default: 0
  },
  width: {
    type: Number,
    default: 16
  },
  height: {
    type: Number,
    default: 16
  }
})
</script>

<style scoped lang="scss">
.details-icons-box {
  .icon {
    display: block;
    width: 100%;
    height: 100%;
  }
}
</style>