<template>
  <n-modal v-model:show="visible" :mask-closable="false" preset="card" style="width: 450px;" @close="closeModal">
    <template #header>
      <div class="modal-header">
        <span class="modal-title">注册</span>
      </div>
    </template>
    <n-form class="register-form" ref="formRef" :model="formValue" :show-label="false" :rules="rules">
      <!-- 手机号输入框 -->
      <n-form-item path="phoneNumber">
        <n-input v-model:value="formValue.phoneNumber" placeholder="请输入手机号码" size="large" clearable />
      </n-form-item>
      <!-- 验证码输入框 -->
      <n-form-item path="verifyCode">
        <n-input v-model:value="formValue.verifyCode" placeholder="请输入验证码" size="large" clearable>
          <template #suffix>
            <n-button text :disabled="countdown > 0" @click="sendVerifyCode" class="verify-btn">
              {{ countdown > 0 ? `${countdown}s后重发` : '获取验证码' }}
            </n-button>
          </template>
        </n-input>
      </n-form-item>
      <!-- 用户名输入框 -->
      <!-- <n-form-item path="userName">
        <n-input v-model:value="formValue.userName" placeholder="请输入用户名" size="large" clearable />
      </n-form-item> -->
      <!-- 密码输入框 -->
      <n-form-item path="password">
        <n-input v-model:value="formValue.password" type="password" placeholder="请输入6-16位包含英文、数字两种组合" size="large"
          show-password-on="click" clearable />
      </n-form-item>
      <!-- 服务协议 -->
      <n-form-item class="agreement" path="agreeTerms">
        <div class="agreement-container">
          <n-checkbox v-model:checked="formValue.agreeTerms" size="small">
            注册即同意本平台的
            <a href="#" class="agreement-link">《用户服务协议》</a>
          </n-checkbox>
        </div>
      </n-form-item>
      <!-- 注册按钮 -->
      <n-form-item>
        <n-button type="primary" size="large" block :loading="loading" @click="handleRegister" class="register-btn">
          注册
        </n-button>
      </n-form-item>
      <!-- 登录链接 -->
      <div class="login-link">
        <a href="#" @click="switchToLogin">已有账号？立即登录</a>
      </div>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage, type FormInst, type FormItemRule } from 'naive-ui'
import { getSmsCodeApi, registerApi } from '@/api/user'
import { useUserStore } from '@/stores/user'
import { useTaskStore } from '@/stores/task'
import { validateAgreement, validatePassword, validatePhone, validateUserName, validateVerifyCode } from '@/utils/validate'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'switch-to-login'): void
}
const taskStore = useTaskStore()
const userStore = useUserStore()
const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const message = useMessage()
const formRef = ref<FormInst | null>(null)

// 响应式数据
const formValue = ref({
  phoneNumber: '',
  verifyCode: '',
  // userName: '',
  password: '',
  agreeTerms: true
})

const loading = ref(false)
const countdown = ref(0)

// 表单校验规则
const rules = {
  phoneNumber: [
    { key: 'phoneNumber', required: true, validator: validatePhone, trigger: ['blur', 'input'] }
  ],
  verifyCode: [
    { required: true, validator: validateVerifyCode, trigger: ['blur', 'input'] }
  ],
  // userName: [
  //   { required: true, validator: validateUserName, trigger: ['blur', 'input'] }
  // ],
  password: [
    { required: true, validator: validatePassword, trigger: ['blur', 'input'] }
  ],
  agreeTerms: [
    { required: true, validator: validateAgreement, trigger: ['change'] }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 方法
const closeModal = () => {
  formValue.value.phoneNumber = ''
  formValue.value.verifyCode = ''
  // formValue.value.userName = ''
  formValue.value.password = ''
  formValue.value.agreeTerms = false
  visible.value = false
}

// 获取验证码
const sendVerifyCode = async () => {

  try {
    formRef.value?.validate(
      async (errors) => {
        if (!errors) {
          await getSmsCodeApi({
            phone: formValue.value.phoneNumber,
            businessType: 'CUS_SMS_CODE_REG'
          })
          message.success('验证码已发送')
          // 开始倒计时
          countdown.value = 60
          const timer = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
              clearInterval(timer)
            }
          }, 1000)
        }
      },
      (rule) => {
        return rule?.key === 'phoneNumber'
      }
    )
  } catch (error) {
    message.error('验证码发送失败')
  }

  // try {
  //   const res = await getSmsCodeApi({
  //     phone: formValue.value.phoneNumber,
  //     businessType: 'CUS_SMS_CODE_REG'
  //   })
  //   message.success('验证码已发送')
  //   // 开始倒计时
  //   countdown.value = 60
  //   const timer = setInterval(() => {
  //     countdown.value--
  //     if (countdown.value <= 0) {
  //       clearInterval(timer)
  //     }
  //   }, 1000)
  // } catch (error) {
  //   message.error('验证码发送失败')
  // }
}

// 注册
const handleRegister = (e: MouseEvent) => {
  e.preventDefault()
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      loading.value = true
      try {
        const { data } = await registerApi({
          phone: formValue.value.phoneNumber,
          // userName: formValue.value.userName,
          smsCode: formValue.value.verifyCode,
          pwd: formValue.value.password
        })
        console.log('注册成功', data)
        userStore.setUserData(data.token, data.userDetail)
        taskStore.getTaskTodoList()
        message.success('注册成功')
        closeModal()
      } catch (error) {

      } finally {
        loading.value = false
      }
    }
  })
}

const switchToLogin = () => {
  emit('switch-to-login')
}
</script>

<style lang="scss" scoped>
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 30px 40px 0 40px;

  .modal-title {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
  }

  .close-icon {
    font-size: 20px;
    color: #999999;
    cursor: pointer;

    &:hover {
      color: #333333;
    }
  }
}

.register-form {
  padding: 20px 40px;

  .verify-btn {
    color: #FB2B1F;
    font-size: 14px;

    &:disabled {
      color: #cccccc;
    }
  }

  .agreement :deep(.n-form-item-feedback-wrapper) {
    margin-top: -10px;
  }

  .agreement :deep(.n-form-item-blank) {
    margin-top: -10px;
  }

  .agreement {

    .agreement-container {
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
    }

    .agreement-link {
      color: #FB2B1F;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .register-btn {
    margin-top: 5px;
    background: #FB2B1F;
    border: none;
    border-radius: 8px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;

    &:hover {
      background: #e02419;
    }
  }

  .login-link {
    text-align: center;

    a {
      color: #666666;
      text-decoration: none;
      font-size: 14px;

      &:hover {
        color: #FB2B1F;
        text-decoration: underline;
      }
    }
  }
}
</style>
