import request from '@/request/request'

interface LivePageType {
  current?: number
  size?: number
  [property: string]: any
}
// 分页查询正在直播的比赛
export const getLivePageApi = (params: LivePageType) => {
  return request.get<any>('/live-customer/foot/getLivePage.e', { params })
}

interface LiveListType {
  liveTypeEnum: string
}
// 通过比赛类型查询正在直播的比赛
export const getLivePageByTypeApi = (params: LiveListType) => {
  return request.get<any>('/live-customer/foot/getLiveByType.e', { params })
}