// 这个脚本可以添加一些自定义的辅助方法
const fs = require('fs');
const path = require('path');

const protoPath = path.join(__dirname, '../src/proto/definitions.js');
let content = fs.readFileSync(protoPath, 'utf8');

// 添加自定义辅助方法
content = `
// Helper function to create a message header
function createHeader(type) {
  const MessageType = root.lookupEnum("im.protocol.common.MessageType");
  
  return {
    message_id: generateUUID(),
    timestamp: Date.now(),
    type: MessageType.values[type],
    version: 1
  };
}

// Helper function to create a login request message
function createLoginRequest(token, devicePlatform) {
  return {
    header: createHeader("LOGIN_REQUEST"),
    login_request: {
      token: token,
      device_platform: parseInt(devicePlatform)
    }
  };
}

// Helper function to create a chat message
function createChatMessage(messageType, senderId, receiverId, content, isGroup) {
  return {
    header: createHeader("MESSAGE"),
    chat_message: {
      chat_message_type: parseInt(messageType),
      sender_id: senderId,
      receiver_id: receiverId,
      content: content,
      sequence: Date.now(),
      is_group: isGroup,
      extra: ""
    }
  };
}

// Helper function to create a heartbeat message
function createHeartbeatMessage() {
  return {
    header: createHeader("HEARTBEAT"),
    heartbeat: "ping"
  };
}

// Helper function to create a message acknowledgment
function createMessageAck(messageId, success, sequence) {
  return {
    header: createHeader("ACK"),
    message_ack: {
      message_id: messageId,
      success: success,
      sequence: sequence || Date.now()
    }
  };
}

// Helper function to encode a message to binary
function encodeMessage(message) {
  try {
    // Get the IMMessage type
    const IMMessage = root.lookupType("im.protocol.IMMessage");
    
    // Verify the message
    const errMsg = IMMessage.verify(message);
    if (errMsg) {
      throw Error(errMsg);
    }
    
    // Create the message
    const imMessage = IMMessage.create(message);
    
    // Encode the message
    return IMMessage.encode(imMessage).finish();
  } catch (error) {
    console.error("Failed to encode message:", error);
    return null;
  }
}

// Helper function to decode a binary message
function decodeMessage(binary) {
  try {
    // Get the IMMessage type
    const IMMessage = root.lookupType("im.protocol.IMMessage");
    
    // Decode the message
    const message = IMMessage.decode(binary);
    
    // Convert to plain object
    return IMMessage.toObject(message, {
      longs: String,
      enums: String,
      defaults: true
    });
  } catch (error) {
    console.error("Failed to decode message:", error);
    return null;
  }
}

// Helper function to generate a UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Initialize function for browser use
async function initProtobuf() {
  // In browser, protobuf is already loaded
  // Just return true as the root is already available
  return true;
}

// Export the helper functions
module.exports.createHeader = createHeader;
module.exports.createLoginRequest = createLoginRequest;
module.exports.createChatMessage = createChatMessage;
module.exports.createHeartbeatMessage = createHeartbeatMessage;
module.exports.createMessageAck = createMessageAck;
module.exports.encodeMessage = encodeMessage;
module.exports.decodeMessage = decodeMessage;
module.exports.generateUUID = generateUUID;
module.exports.initProtobuf = initProtobuf;
`;

fs.writeFileSync(protoPath, content);
console.log('Proto helpers appended');