import { createChatMessage, encodeMessage, decodeMessage } from '../proto/definitions';

class ChatService {
  private socket: WebSocket | null;
  private messageHandlers: Set<(message: any) => void>;
  constructor() {
    this.socket = null;
    this.messageHandlers = new Set();
  }

  connect(url, token) {
    return new Promise<void>((resolve, reject) => {
      this.socket = new WebSocket(url);

      this.socket.binaryType = 'arraybuffer';

      this.socket.onopen = () => {
        this._sendLogin(token);
        resolve();
      };

      this.socket.onmessage = (event) => {
        this._handleMessage(event.data);
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };
    });
  }

  _sendLogin(token) {
    const loginRequest = {
      header: {
        type: 'LOGIN_REQUEST',
        timestamp: Date.now()
      },
      login_request: {
        token: token,
        device_platform: 'WEB'
      }
    };
    this.send(loginRequest);
  }

  send(message) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      const encoded = encodeMessage(message);
      this.socket.send(encoded);
    } else {
      console.error('WebSocket is not open');
    }
  }

  sendChatMessage(content, receiverId, type = 'TEXT') {
    const chatMsg = createChatMessage(content, receiverId, type);
    const message = {
      header: {
        type: 'MESSAGE',
        timestamp: Date.now()
      },
      chat_message: chatMsg
    };
    this.send(message);
  }

  _handleMessage(data) {
    try {
      const message = decodeMessage(new Uint8Array(data));
      this.messageHandlers.forEach(handler => handler(message));
    } catch (error) {
      console.error('Failed to decode message:', error);
    }
  }

  onMessage(handler) {
    this.messageHandlers.add(handler);
    return () => this.messageHandlers.delete(handler);
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
}

export const chatService = new ChatService();