<template>
  <div class="personal-center-wrapper">
    <div class="personal-center">
      <!-- 左侧导航菜单 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h2>个人中心</h2>
        </div>
        <nav class="sidebar-nav">
          <router-link to="/user/dashboard" class="nav-item" active-class="active">
            <span class="nav-icon dashboard-icon"></span>
            <span>我的首页</span>
          </router-link>
          <router-link to="/user/profile" class="nav-item" active-class="active">
            <span class="nav-icon profile-icon"></span>
            <span>我的资料</span>
          </router-link>
          <router-link to="/user/messages" class="nav-item" active-class="active">
            <span class="nav-icon messages-icon"></span>
            <span>我的私信</span>
          </router-link>
          <router-link to="/user/follows" class="nav-item" active-class="active">
            <span class="nav-icon follows-icon"></span>
            <span>主播关注</span>
          </router-link>
          <router-link to="/user/appointments" class="nav-item" active-class="active">
            <span class="nav-icon appointments-icon"></span>
            <span>比赛关注</span>
          </router-link>
          <router-link to="/user/feedback" class="nav-item" active-class="active">
            <span class="nav-icon feedback-icon"></span>
            <span>我的反馈</span>
          </router-link>
          <router-link to="/user/live-appointments" v-if="isAuthor" class="nav-item" active-class="active">
            <span class="nav-icon live-appointments-icon"></span>
            <span>预约直播</span>
          </router-link>
          <router-link to="/user/live-settings" v-if="isAuthor" class="nav-item" active-class="active">
            <span class="nav-icon live-settings-icon"></span>
            <span>直播设置</span>
          </router-link>
        </nav>
      </div>
      <!-- 右侧内容区域 -->
      <div class="main-content">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()

const isAuthor = computed(() => userStore.isAuthor)


</script>

<style lang="scss" scoped>
.personal-center-wrapper {
  width: 100%;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.personal-center {
  width: 1200px;
  display: flex;
  min-height: 600px;
  position: relative;
}

// .sidebar-nav-warp {
//   width: 240px;
//   height: 700px;
//   background: #ffffff;
// }

.sidebar {
  width: 240px;
  min-height: 100%;
  background: #ffffff;
  position: fixed; // 改为fixed定位
  top: auto; // 配合wrapper的padding-top
  left: 50%; // 居中处理
  transform: translateX(-600px); // 向左偏移父容器宽度的一半(1200/2=600)以实现对齐

  .sidebar-header {
    padding: 30px 20px 20px;
    border-bottom: 1px solid #f0f0f0;

    h2 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .sidebar-nav {
    padding: 20px 0;

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.2s;
      color: #666;
      text-decoration: none;

      &:hover {
        background: #f8f8f8;
        color: #333;
      }

      &.active {
        background: #FB2B1F;
        color: #ffffff;

        .dashboard-icon {
          background-image: url('@/static/user/dashboard-icon-active.png');
        }

        .profile-icon {
          background-image: url('@/static/user/profile-icon-active.png');
        }

        .messages-icon {
          background-image: url('@/static/user/message-icon-active.png');
        }

        .follows-icon {
          background-image: url('@/static/user/follows-icon-active.png');
        }

        .appointments-icon {
          background-image: url('@/static/user/appointments-icon-active.png');
        }

        .feedback-icon {
          background-image: url('@/static/user/feedback-icon-active.png');
        }

        .live-appointments-icon {
          background-image: url('@/static/user/live-icon-active.png');
        }

        .live-settings-icon {
          background-image: url('@/static/user/live-settings-icon-active.png');
        }
      }

      .nav-icon {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      .dashboard-icon {
        background-image: url('@/static/user/dashboard-icon.png');
      }

      .profile-icon {
        background-image: url('@/static/user/profile-icon.png');
      }

      .messages-icon {
        background-image: url('@/static/user/message-icon.png');
      }

      .follows-icon {
        background-image: url('@/static/user/follows-icon.png');
      }

      .appointments-icon {
        background-image: url('@/static/user/appointments-icon.png');
      }

      .feedback-icon {
        background-image: url('@/static/user/feedback-icon.png');
      }

      .live-appointments-icon {
        background-image: url('@/static/user/live-icon.png');
      }

      .live-settings-icon {
        background-image: url('@/static/user/live-settings-icon.png');
      }

      span {
        font-size: 14px;
      }
    }
  }
}

.main-content {
  flex: 1;
  margin-left: 240px; // 改用margin-left替代padding-left
  padding: 0 40px;
}
</style>