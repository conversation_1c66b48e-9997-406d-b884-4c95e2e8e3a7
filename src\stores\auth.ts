import { defineStore } from 'pinia'

interface AuthStateType {
  // 登录弹窗显示状态
  showAuthModal: boolean
  // 弹窗类型
  authModalType: 'login' | 'register'
}

export const useAuthStore = defineStore('authStore', {
  state: (): AuthStateType => {
    return {
      showAuthModal: false,
      authModalType: 'login'
    }
  },
  
  getters: {
    // 获取弹窗显示状态
    getShowAuthModal: (state) => state.showAuthModal,
    // 获取弹窗类型
    getAuthModalType: (state) => state.authModalType
  },
  
  actions: {
    // 显示登录弹窗
    showLogin() {
      this.authModalType = 'login'
      this.showAuthModal = true
    },
    
    // 显示注册弹窗
    showRegister() {
      this.authModalType = 'register'
      this.showAuthModal = true
    },
    
    // 隐藏弹窗
    hideAuthModal() {
      this.showAuthModal = false
    },
    
    // 设置弹窗类型
    setAuthModalType(type: 'login' | 'register') {
      this.authModalType = type
    }
  }
})
