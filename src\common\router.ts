import HomeView from '../views/HomeView.vue'

const NotFound = () => import('../components/view/404/NotFoundView.vue')
const HomePageView = () => import('../components/view/home/<USER>')
const AllView = () => import('../components/view/all/AllView.vue')
const UserCenterView = () => import('../components/view/user/UserCenterView.vue')
const UserDashboard = () => import('../components/view/user/UserDashboard.vue')
const UserProfile = () => import('../components/view/user/UserProfile.vue')
const UserMessages = () => import('../components/view/user/UserMessages.vue')
const UserFollows = () => import('../components/view/user/UserFollows.vue')
const UserAppointments = () => import('../components/view/user/UserAppointments.vue')
const UserFeedback = () => import('../components/view/user/UserFeedback.vue')
const LiveAppointments = () => import('../components/view/user/LiveAppointments.vue')
const LiveSettings = () => import('../components/view/user/LiveSettings.vue')
const ModifyPassword = () => import('../components/view/user/ModifyPassword.vue')
const ModifyPhone = () => import('../components/view/user/ModifyPhone.vue')
const ScheduleView = () => import('../components/view/schedule/scheduleView.vue')
const RoomView = () => import('../components/view/detail/DetailView.vue')
const GlobalModifyPassword = () => import('../components/view/user/GlobalModifyPassword.vue')
const DownLoadView = () => import('../components/view/download/DownLoadView.vue')

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    redirect: '/home',
    children: [
      {
        name: 'homepage',
        path: '/home',
        component: HomePageView,
        meta: {
          title: '首页'
        }
      },
      {
        name: 'allpage',
        path: '/all',
        component: AllView,
        meta: {
          title: '全部直播'
        }
      },
      {
        name: 'schedule',
        path: '/schedule',
        component: ScheduleView,
        meta: {
          title: '赛程'
        }
      },
      {
        name: 'room',
        path: '/room',
        component: RoomView,
        meta: {
          title: '直播间'
        }
      },
      {
        name: 'global-modify-password',
        path: '/global-modify-password',
        component: GlobalModifyPassword,
        meta: {
          title: '修改密码'
        }
      },
      {
        name: 'download',
        path: 'download',
        component: DownLoadView,
        meta: {
          title: '下载APP'
        }
      },
      {
        name: 'usercenter',
        path: '/user',
        component: UserCenterView,
        redirect: '/user/dashboard',
        meta: {
          title: '个人中心'
        },
        children: [
          {
            name: 'user-dashboard',
            path: 'dashboard',
            component: UserDashboard,
            meta: {
              title: '我的首页'
            }
          },
          {
            name: 'user-profile',
            path: 'profile',
            component: UserProfile,
            meta: {
              title: '我的资料'
            }
          },
          {
            name: 'user-messages',
            path: 'messages',
            component: UserMessages,
            meta: {
              title: '我的私信'
            }
          },
          {
            name: 'user-follows',
            path: 'follows',
            component: UserFollows,
            meta: {
              title: '主播关注'
            }
          },
          {
            name: 'user-appointments',
            path: 'appointments',
            component: UserAppointments,
            meta: {
              title: '比赛关注'
            }
          },
          {
            name: 'user-feedback',
            path: 'feedback',
            component: UserFeedback,
            meta: {
              title: '我的反馈'
            }
          },
          {
            name: 'live-appointments',
            path: 'live-appointments',
            component: LiveAppointments,
            meta: {
              title: '预约直播'
            }
          },
          {
            name: 'live-settings',
            path: 'live-settings',
            component: LiveSettings,
            meta: {
              title: '直播设置'
            }
          },
          {
            name: 'modify-password',
            path: 'modify-password',
            component: ModifyPassword,
            meta: {
              title: '修改密码'
            }
          },
          {
            name: 'modify-phone',
            path: 'modify-phone',
            component: ModifyPhone,
            meta: {
              title: '修改手机号'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '404错误啦'
    }
  }
]
export default routes
