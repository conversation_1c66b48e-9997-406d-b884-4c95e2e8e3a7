<template>
  <div class="default-logo-box" :style="{ width: width, height: height }">
    <img v-if="type == 'player'" class="icon" :src="props.logo ? props.logo : defaultPlayerLogo" />
    <img v-if="type == 'team'" class="icon" :src="props.logo ? props.logo : defaultTeamLogo" />
    <img v-if="type == 'anchor'" class="icon" :src="props.logo ? props.logo : defaultAvatarLogo" />
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  logo: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'player'
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '100%'
  }
})
import defaultPlayerLogo from '@/static/index/default-player-logo.png';
import defaultAvatarLogo from '@/static/live/<EMAIL>';
import defaultTeamLogo from '@/static/header/<EMAIL>';


</script>
<style lang="scss" scoped>
.default-logo-box {
  .icon {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    // object-fit: cover;
    display: block;
  }
}
</style>