<template>
  <div class="level-box">
    <img class="icon" :src="getLevelIcon(levelSection)" />
    <span class="label" :class="getLevelClass()">{{ props.level }}</span>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import level1 from '@/static/live/Component <EMAIL>'
import level2 from '@/static/live/Component <EMAIL>'
import level3 from '@/static/live/Component <EMAIL>'
import level4 from '@/static/live/Component <EMAIL>'
import level5 from '@/static/live/Component <EMAIL>'
import level6 from '@/static/live/Component <EMAIL>'
import level7 from '@/static/live/Component <EMAIL>'
import level8 from '@/static/live/Component <EMAIL>'
const props = defineProps({
  level: {
    type: Number,
    default: 1
  }
})
const getLevelIcon = (level: number) => {
  switch (level) {
    case 0:
      return level1
    case 1:
      return level2
    case 2:
      return level3
    case 3:
      return level4
    case 4:
      return level4
    case 5:
      return level5
    case 6:
      return level5
    case 7:
      return level6
    case 8:
      return level6
    case 9:
      return level7
    case 10:
      return level8
  }
}
const getLevelClass = () => {
  return `level${levelSection.value}`
}
let levelSection = ref(0)
watch(() => props.level, (newVal, oldVal) => {
  if (Math.floor(newVal / 10) > 10) {
    levelSection.value = 10
  } else {
    levelSection.value = Math.floor(newVal / 10)
  }
}, { immediate: true })
</script>
<style lang="scss" scoped>
.level-box {
  position: relative;
  // display: inline-block;
  margin-right: 4px;

  .icon {
    display: block;
    width: 44px;
    height: 20px;
  }

  .label {
    position: absolute;
    width: 30px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    top: 0;
    right: 0;
    // transform: translate(0, -50%);
    font-size: 12px;

    &.level0 {
      color: #909090;
    }

    &.level1 {
      color: #7FD0D8;
    }

    &.level2 {
      color: #81BAEC;
    }

    &.level3 {
      color: #EEAF3F;
    }

    &.level4 {
      color: #EEAF3F;
    }

    &.level5 {
      color: #FB8A66;
    }

    &.level6 {
      color: #FB8A66;
    }

    &.level7 {
      color: #87A6FE;
    }

    &.level8 {
      color: #87A6FE;
    }

    &.level9 {
      color: #AD7333;
    }

    &.level10 {
      color: #AE4BE3;
    }
  }
}
</style>