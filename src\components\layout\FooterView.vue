<template>
  <footer class="footer">
    <div class="footer-container">
      <!-- Logo区域 -->
      <div class="footer-logo">
        <div class="logo-icon"></div>
      </div>

      <!-- 导航链接 -->
      <div class="footer-nav">
        <a href="/about" class="footer-link">关于主播教程</a>
        <a href="/live-rules" class="footer-link">直播规范说明</a>
        <a href="/user-agreement" class="footer-link">用户协议说明</a>
      </div>

      <!-- 版权信息 -->
      <div class="footer-copyright">
        <p>Copyright© 2025 youhao.live.All rights reserved.</p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
</script>

<style lang="scss" scoped>
.footer {
  width: 100%;
  background: #fff;
  padding: 40px 0;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;

    .footer-logo {
      .logo-icon {
        width: 48px;
        height: 48px;
        background: #ffa500;
        border-radius: 8px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 24px;
          height: 24px;
          background: #ffffff;
          border-radius: 50%;
        }

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 12px;
          height: 12px;
          background: #ffa500;
          border-radius: 50%;
        }
      }
    }

    .footer-nav {
      display: flex;
      gap: 40px;
      align-items: center;

      .footer-link {
        color: #333;
        text-decoration: none;
        font-size: 14px;
        font-weight: 400;
        transition: color var(--duration-200);

        &:hover {
          color: #ffa500;
        }
      }
    }

    .footer-copyright {
      p {
        margin: 0;
        color: #999999;
        font-size: 12px;
        font-weight: 400;
        text-align: center;
      }
    }
  }

  @media (max-width: 768px) {
    .footer-container {
      .footer-nav {
        flex-direction: column;
        gap: 16px;
      }
    }
  }
}
</style>