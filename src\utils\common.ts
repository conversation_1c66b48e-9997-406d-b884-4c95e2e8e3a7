import CryptoJS from 'crypto-js'

export const isEmptyObject = (obj: object): boolean => {
  return Object.keys(obj).length === 0
}

export const getUrlBeforeParms = function (url: string) {
  console.log(url.split('?'), 'urlStrurlStrurlStr')
  // 通过 ? 分割获取后面的参数字符串
  const urlStr = url.split('?')[0]

  if (!urlStr) return {}
  // 创建空对象存储参数
  const obj = {}
  // 再通过 & 将每一个参数单独分割出来
  const paramsArr = urlStr.split('&')
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    const arr = paramsArr[i].split('=')
    obj[arr[0]] = arr[1]
  }
  return obj
}
export const getUrlAfterParms = function (url: string) {
  // 通过 ? 分割获取后面的参数字符串
  const urlStr = url.split('?')[1]

  if (!urlStr) return {}
  // 创建空对象存储参数
  const obj = {}
  // 再通过 & 将每一个参数单独分割出来
  const paramsArr = urlStr.split('&')
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    const arr = paramsArr[i].split('=')
    obj[arr[0]] = arr[1]
  }
  return obj
}

//复制
export const copyText = function (value: string) {
  navigator.clipboard.writeText(value).then(() => {
    window.$message.success('复制成功')
  })
}

export const isMobile = function () {
  const flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
  )
  return flag
}

// export const debounce = function (func, delay) {
//   let timer
//   return function () {
//     clearTimeout(timer)
//     timer = setTimeout(() => {
//       func.apply(this, arguments)
//     }, delay)
//   }
// }

export const debounce = function <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout

  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}
export const debounceWithFirst = function <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  let isFirst = true;

  return function (this: any, ...args: Parameters<T>) {
    if (isFirst) {
      isFirst = false;
      func.apply(this, args);
      return;
    }

    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
};
export const throttle = function <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout | null = null

  return function (this: any, ...args: Parameters<T>) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}
export const throttleWithFirst = function <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let isFirst = true;
  let timer: NodeJS.Timeout | null = null;

  return function (this: any, ...args: Parameters<T>) {
    // 第一次点击立即执行
    if (isFirst) {
      func.apply(this, args);
      isFirst = false;
      return;
    }

    // 后续点击使用节流
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args);
        timer = null;
      }, delay);
    }
  }
}
export const passwordTo32Chars = function (password: string) {
  // 使用 CryptoJS 的 MD5 方法
  const hash = CryptoJS.MD5(password).toString()
  return hash // 将返回32个字符的十六进制字符串
}

export const maskPhoneNumber = function (phone: string) {
  return phone.replace(/(\d{3})(\d{6})(\d{2})/, '$1******$3')
}


//判断奇偶
export function isEven(number) {
  return number % 2 === 0;
}

export function isOdd(number) {
  return number % 2 === 1;
}

export function isSafari() {
  var ua = navigator.userAgent.toLowerCase();

  // 使用正则表达式匹配Safari浏览器的特征，并排除Chrome等浏览器
  var isSafari = /^((?!chrome|android).)*safari/i.test(ua);

  // 也可以考虑使用以下方式，但这种方式可能不如正则表达式准确
  // var isSafari = ua.indexOf('safari') !== -1 && ua.indexOf('chrome') === -1 && ua.indexOf('android') === -1;

  return isSafari;
}

//判断当前数字是否为给定数字数组中的最大值
export const isMaxNumber = (currentValue: number, data: any[], key: string): boolean => {
  if (!currentValue || !data || data.length === 0) return false;

  // 使用点号分割路径
  const [objKey, propKey] = key.split('.');

  // 获取该列所有值
  const columnValues = data.map(item => item[objKey]?.[propKey] || 0);

  // 找出最大值
  const maxValue = Math.max(...columnValues);
  // 返回当前值是否为最大值，且大于0
  return currentValue === maxValue && maxValue > 0;
}