import type { GlobalThemeOverrides } from 'naive-ui'
const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: '#EB0000FF',
    primaryColorHover: 'rgba(235, 0, 0, 0.5)',
    primaryColorPressed: '#D90E0EFF',
    primaryColorSuppl: '#EB0000FF'
  },
  Message: {
    colorInfo: '#1D90E3FF',
    colorSuccess: '#18A058FF',
    colorError: '#D03050FF',
    textColorError: 'rgba(255, 255, 255, 1)',
    iconColorError: '#FFFFFFFF',
    textColorSuccess: 'rgba(255, 255, 255, 1)',
    iconColorSuccess: '#FFFFFFFF',
    iconColorInfo: '#FFFFFFFF',
    textColorInfo: 'rgba(255, 255, 255, 1)',
    colorWarning: '#F0A020FF',
    textColorWarning: 'rgba(255, 255, 255, 1)',
    iconColorWarning: '#FFFFFFFF'
  }
}

export default themeOverrides
