<template>
  <n-modal v-model:show="showModal" preset="card" title="预约直播" style="width: 600px;" :bordered="false">
    <div class="appointment-form">
      <!-- 直播标题 -->
      <div class="form-item">
        <div class="form-label">
          <span class="required">*</span>直播标题
        </div>
        <n-input v-model:value="formData.title" placeholder="请输入直播标题" maxlength="50" />
      </div>

      <!-- 公告 -->
      <div class="form-item">
        <div class="form-label">公告</div>
        <n-input v-model:value="formData.announcement" placeholder="请输入公告-首备字段-用于直播间的提示" maxlength="100" />
      </div>

      <!-- 直播封面 -->
      <div class="form-item">
        <div class="form-label">
          <span class="required">*</span>直播封面
        </div>
        <div class="upload-area">
          <n-upload v-model:file-list="fileList" action="https://www.mocky.io/v2/5e4bafc63100007100d8b70f"
            list-type="image-card" :max="1" accept="image/*" @change="handleUploadChange" />
        </div>
        <div class="upload-tip">
          请上传大小不超过5MB格式为png/jpg/jpeg的文件
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <n-button type="primary" size="large" block @click="submitAppointment">
          去预约
        </n-button>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NModal, NButton, NIcon, NInput, NUpload, type UploadFileInfo } from 'naive-ui'

interface AppointmentForm {
  title: string
  announcement: string
}

// 定义 emits
const emit = defineEmits<{
  close: []
}>()

const showModal = ref(true)
const fileList = ref<UploadFileInfo[]>([])

const formData = ref<AppointmentForm>({
  title: '',
  announcement: ''
})

const closeModal = () => {
  showModal.value = false
  emit('close')
}

const handleUploadChange = (options: { fileList: UploadFileInfo[] }) => {
  fileList.value = options.fileList
}

const submitAppointment = () => {
  // 表单验证
  if (!formData.value.title.trim()) {
    // 这里可以使用 n-message 显示错误提示
    console.log('请输入直播标题')
    return
  }

  if (fileList.value.length === 0) {
    console.log('请上传直播封面')
    return
  }

  // 提交预约数据
  console.log('提交预约:', {
    title: formData.value.title,
    announcement: formData.value.announcement,
    cover: fileList.value[0]
  })

  // 提交成功后关闭弹窗
  closeModal()
}
</script>

<style lang="scss" scoped>
.appointment-form {
  .form-item {
    margin-bottom: 24px;

    .form-label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      font-weight: 500;

      .required {
        color: #FF4D4F;
        margin-right: 4px;
      }
    }
  }

  .upload-area {
    margin-bottom: 8px;
  }

  .upload-tip {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
  }

  .submit-section {
    margin-top: 32px;

    :deep(.n-button) {
      background: #FB2B1F;

      &:hover {
        background: #E02419;
      }
    }
  }
}
</style>
