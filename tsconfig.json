{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": ["env.d.ts","src/**/*","src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"],
  "compilerOptions": {
    "baseUrl": "./",
    "composite": true,
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["type/*"]
    },
    "ignoreDeprecations": "5.0",
    "types": ["node"],
    "noImplicitAny": false,
  },
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
}
