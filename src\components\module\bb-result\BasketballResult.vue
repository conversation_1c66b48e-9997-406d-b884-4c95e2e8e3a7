<template>
  <div class="basketball-result" v-if="homeList.length > 0 && awayList.length > 0">
    <!-- 球员统计 -->
    <div class="section">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">球员统计</span>
      </div>
      <!-- 客队球员统计 -->
      <div class="team-stats">
        <div class="team-header">
          <img class="logo" :src="liveDetail.awayLogo" />
          <span class="team-name">{{ liveDetail.awayName }}</span>
        </div>
        <div class="player-table-container">
          <table class="player-table">
            <thead>
              <tr class="table-header">
                <th class="player-col">球员</th>
                <th class="time-col">时间</th>
                <th class="stat-col">得分</th>
                <th class="stat-col">篮板</th>
                <th class="stat-col">助攻</th>
                <th class="stat-col">投篮</th>
                <th class="stat-col">三分</th>
                <th class="stat-col">罚球</th>
                <th class="stat-col">抢断</th>
                <th class="stat-col">盖帽</th>
                <th class="stat-col">失误</th>
                <th class="stat-col">犯规</th>
                <th class="stat-col">前板</th>
                <th class="stat-col">后板</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="player in awayList" :key="player.id"
                :class="{ 'starter-row': player.substitute === 0, 'bench-row': player.substitute === 1 }">
                <td class="player-name">{{ player.playerName }}</td>
                <td class="time-data">{{ player.duration }}</td>
                <td class="points-data">{{ player.points }}</td>
                <td class="stat-data">{{ player.rebounds }}</td>
                <td class="stat-data">{{ player.assists }}</td>
                <td class="stat-data">{{ player.fieldGoalsScored }}-{{ player.fieldGoalsTotal }}</td>
                <td class="stat-data">{{ player.threePointersScored }}-{{ player.threePointersTotal }}</td>
                <td class="stat-data">{{ player.freeThrowsScored }}-{{ player.freeThrowsTotal }}</td>
                <td class="stat-data">{{ player.steals }}</td>
                <td class="stat-data">{{ player.blocks }}</td>
                <td class="stat-data">{{ player.turnovers }}</td>
                <td class="stat-data">{{ player.foul }}</td>
                <td class="stat-data">{{ player.offensiveRebounds }}</td>
                <td class="stat-data">{{ player.defensiveRebounds }}</td>
              </tr>
              <tr class="total-row">
                <td class="total-label"><strong>合计</strong></td>
                <td class="total-data">{{ awayTeamTotals.totalMinutes }}</td>
                <td class="total-data">{{ awayTeamTotals.totalPoints }}</td>
                <td class="total-data">{{ awayTeamTotals.totalRebounds }}</td>
                <td class="total-data">{{ awayTeamTotals.totalAssists }}</td>
                <td class="total-data">{{ awayTeamTotals.totalFieldGoals }}</td>
                <td class="total-data">{{ awayTeamTotals.totalThreePointers }}</td>
                <td class="total-data">{{ awayTeamTotals.totalFreeThrows }}</td>
                <td class="total-data">{{ awayTeamTotals.totalSteals }}</td>
                <td class="total-data">{{ awayTeamTotals.totalBlocks }}</td>
                <td class="total-data">{{ awayTeamTotals.totalTurnovers }}</td>
                <td class="total-data">{{ awayTeamTotals.totalFouls }}</td>
                <td class="total-data">{{ awayTeamTotals.totalOffensiveRebounds }}</td>
                <td class="total-data">{{ awayTeamTotals.totalDefensiveRebounds }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <!-- 主队球员统计 -->
      <div class="team-stats">
        <div class="team-header">
          <img class="logo" :src="liveDetail.homeLogo" />
          <span class="team-name">{{ liveDetail.homeName }}</span>
        </div>
        <div class="player-table-container">
          <table class="player-table">
            <thead>
              <tr class="table-header">
                <th class="player-col">球员</th>
                <th class="time-col">时间</th>
                <th class="stat-col">得分</th>
                <th class="stat-col">篮板</th>
                <th class="stat-col">助攻</th>
                <th class="stat-col">投篮</th>
                <th class="stat-col">三分</th>
                <th class="stat-col">罚球</th>
                <th class="stat-col">抢断</th>
                <th class="stat-col">盖帽</th>
                <th class="stat-col">失误</th>
                <th class="stat-col">犯规</th>
                <th class="stat-col">前板</th>
                <th class="stat-col">后板</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="player in homeList" :key="player.id"
                :class="{ 'starter-row': player.substitute === 0, 'bench-row': player.substitute === 1 }">
                <td class="player-name">{{ player.playerName }}</td>
                <td class="time-data">{{ player.duration }}</td>
                <td class="points-data">{{ player.points }}</td>
                <td class="stat-data">{{ player.rebounds }}</td>
                <td class="stat-data">{{ player.assists }}</td>
                <td class="stat-data">{{ player.fieldGoalsScored }}-{{ player.fieldGoalsTotal }}</td>
                <td class="stat-data">{{ player.threePointersScored }}-{{ player.threePointersTotal }}</td>
                <td class="stat-data">{{ player.freeThrowsScored }}-{{ player.freeThrowsTotal }}</td>
                <td class="stat-data">{{ player.steals }}</td>
                <td class="stat-data">{{ player.blocks }}</td>
                <td class="stat-data">{{ player.turnovers }}</td>
                <td class="stat-data">{{ player.foul }}</td>
                <td class="stat-data">{{ player.offensiveRebounds }}</td>
                <td class="stat-data">{{ player.defensiveRebounds }}</td>
              </tr>
              <tr class="total-row">
                <td class="total-label"><strong>合计</strong></td>
                <td class="total-data">{{ homeTeamTotals.totalMinutes }}</td>
                <td class="total-data">{{ homeTeamTotals.totalPoints }}</td>
                <td class="total-data">{{ homeTeamTotals.totalRebounds }}</td>
                <td class="total-data">{{ homeTeamTotals.totalAssists }}</td>
                <td class="total-data">{{ homeTeamTotals.totalFieldGoals }}</td>
                <td class="total-data">{{ homeTeamTotals.totalThreePointers }}</td>
                <td class="total-data">{{ homeTeamTotals.totalFreeThrows }}</td>
                <td class="total-data">{{ homeTeamTotals.totalSteals }}</td>
                <td class="total-data">{{ homeTeamTotals.totalBlocks }}</td>
                <td class="total-data">{{ homeTeamTotals.totalTurnovers }}</td>
                <td class="total-data">{{ homeTeamTotals.totalFouls }}</td>
                <td class="total-data">{{ homeTeamTotals.totalOffensiveRebounds }}</td>
                <td class="total-data">{{ homeTeamTotals.totalDefensiveRebounds }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 球队统计 -->
    <div class="section">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">球队统计</span>
      </div>
      <div class="team-stats-table">
        <table class="team-comparison-table">
          <thead>
            <tr class="table-header">
              <th class="team-col">球队名称</th>
              <th class="stat-col">得分</th>
              <th class="stat-col">篮板</th>
              <th class="stat-col">投篮命中率</th>
              <th class="stat-col">三分命中率</th>
              <th class="stat-col">罚球命中率</th>
            </tr>
          </thead>
          <tbody>
            <tr class="team-row">
              <td class="team-name">
                <defaultLogo type="team" :logo="liveDetail.awayLogo" width="30px" height="30px"></defaultLogo>{{
                  liveDetail.awayName }}
              </td>
              <td class="stat-value">{{ awayTeamTotals.totalPoints }}</td>
              <td class="stat-value">{{ awayTeamTotals.totalRebounds }}</td>
              <td class="stat-value">{{ awayTeamTotals.goalsRate }}</td>
              <td class="stat-value">{{ awayTeamTotals.threePointRate }}</td>
              <td class="stat-value">{{ awayTeamTotals.freeThrowRate }}</td>
            </tr>
            <tr class="team-row">
              <td class="team-name">
                <defaultLogo type="team" :logo="liveDetail.homeLogo" width="30px" height="30px"></defaultLogo> {{
                  liveDetail.homeName }}
              </td>
              <td class="stat-value">{{ homeTeamTotals.totalPoints }}</td>
              <td class="stat-value">{{ homeTeamTotals.totalRebounds }}</td>
              <td class="stat-value">{{ homeTeamTotals.goalsRate }}</td>
              <td class="stat-value">{{ homeTeamTotals.threePointRate }}</td>
              <td class="stat-value">{{ homeTeamTotals.freeThrowRate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 各项数据统计 -->
    <div class="section">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">各项最高统计</span>
      </div>
      <div class="detailed-stats">
        <table class="detailed-stats-table">
          <thead>
            <tr class="table-header">
              <th class="team-col">球队名称</th>
              <th class="stat-col">得分</th>
              <th class="stat-col">篮板</th>
              <th class="stat-col">助攻</th>
              <th class="stat-col">抢断</th>
              <th class="stat-col">盖帽</th>
              <th class="stat-col">时间</th>
              <th class="stat-col">失误</th>
              <th class="stat-col">犯规</th>
            </tr>
          </thead>
          <tbody>
            <tr class="team-row">
              <td class="team-name">
                <defaultLogo type="team" :logo="liveDetail.awayLogo" width="30px" height="30px"></defaultLogo>
                {{ liveDetail.awayName }}
              </td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.points.isHighest }">{{
                finalAwayTopStats.points.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.rebounds.isHighest }">{{
                finalAwayTopStats.rebounds.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.assists.isHighest }">{{
                finalAwayTopStats.assists.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.steals.isHighest }">{{
                finalAwayTopStats.steals.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.blocks.isHighest }">{{
                finalAwayTopStats.blocks.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.minutes.isHighest }">{{
                finalAwayTopStats.minutes.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.turnovers.isHighest }">{{
                finalAwayTopStats.turnovers.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalAwayTopStats.fouls.isHighest }">{{
                finalAwayTopStats.fouls.display }}</td>
            </tr>
            <tr class="team-row">
              <td class="team-name">
                <defaultLogo type="team" :logo="liveDetail.homeLogo" width="30px" height="30px"></defaultLogo>
                {{ liveDetail.homeName }}
              </td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.points.isHighest }">{{
                finalHomeTopStats.points.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.rebounds.isHighest }">{{
                finalHomeTopStats.rebounds.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.assists.isHighest }">{{
                finalHomeTopStats.assists.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.steals.isHighest }">{{
                finalHomeTopStats.steals.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.blocks.isHighest }">{{
                finalHomeTopStats.blocks.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.minutes.isHighest }">{{
                finalHomeTopStats.minutes.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.turnovers.isHighest }">{{
                finalHomeTopStats.turnovers.display }}</td>
              <td class="stat-value" :class="{ highlighted: finalHomeTopStats.fouls.isHighest }">{{
                finalHomeTopStats.fouls.display }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 伤停情况 -->
    <!-- <div class="section">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">伤停情况</span>
      </div>
      <div class="injury-section">
        <div class="injury-table">
          <div class="injury-header">
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div class="injury-list">
            <div class="injury-header-row">
              <span>球员</span>
              <span>位置</span>
              <span>原因</span>
            </div>
            <template v-for="item in injuryList" :key="item.id">
              <div class="injury-row" v-if="item && item.type === 2">
                <div class="player-info">
                  <img class="player-avatar" :src="item.playerLogo || defaultPlayerLogo" />
                  <span class="player-name">{{ item.playerName }}</span>
                </div>
                <span class="position">{{ getPositionName(item.position) }}</span>
                <span class="reason">{{ item.reason }}</span>
              </div>
            </template>
</div>
</div>
<div class="injury-table">
  <div class="injury-header">
    <img class="team-logo" :src="liveDetail.homeLogo" />
    <span class="team-name">{{ liveDetail.homeName }}</span>
  </div>
  <div class="injury-list">
    <div class="injury-header-row">
      <span>球员</span>
      <span>位置</span>
      <span>原因</span>
    </div>
    <template v-for="item in injuryList" :key="item.id">
              <div class="injury-row" v-if="item && item.type === 1">
                <div class="player-info">
                  <img class="player-avatar" :src="item.playerLogo || defaultPlayerLogo" />
                  <span class="player-name">{{ item.playerName }}</span>
                </div>
                <span class="position">{{ getPositionName(item.position) }}</span>
                <span class="reason">{{ item.reason }}</span>
              </div>
            </template>
  </div>
</div>
</div>
</div> -->
  </div>
  <div v-else class="none-box">
    <none-box text="暂无赛况数据" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { getPlayerApi, getPlayerStatApi } from '@/api/match'

const props = defineProps({
  matchId: {
    type: Number,
    required: true
  },
  liveDetail: {
    type: Object,
    required: true,
    default: () => ({
      awayLogo: '',
      awayName: '',
      homeLogo: '',
      homeName: '',
      sclassName: ''
    })
  }
})
interface MatchBasketStatPlayerDetailRes {
  /**
   * 未知
   */
  appearance?: number;
  /**
   * 助攻
   */
  assists?: number;
  /**
   * 盖帽
   */
  blocks?: number;
  /**
   * 后篮板
   */
  defensiveRebounds?: number;
  /**
   * 出场时间
   */
  duration?: string;
  /**
   * 投篮命中数
   */
  fieldGoalsScored?: number;
  /**
   * 投篮总计数
   */
  fieldGoalsTotal?: number;
  /**
   * 犯规
   */
  foul?: number;
  /**
   * 罚球命中数
   */
  freeThrowsScored?: number;
  /**
   * 罚球总数
   */
  freeThrowsTotal?: number;
  id?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 前篮板
   */
  offensiveRebounds?: number;
  /**
   * 球员logo
   */
  playerLogo?: string;
  /**
   * 球员名称
   */
  playerName?: string;
  /**
   * 得分
   */
  points?: number;
  /**
   * 未知
   */
  present?: number;
  /**
   * 总篮板
   */
  rebounds?: number;
  /**
   * 球衣号
   */
  shirtNumber?: string;
  /**
   * 抢断
   */
  steals?: number;
  /**
   * 0首发 1.替补
   */
  substitute?: number;
  /**
   * 球队id
   */
  teamId?: number;
  /**
   * 三分命中数
   */
  threePointersScored?: number;
  /**
   * 三分总计数
   */
  threePointersTotal?: number;
  /**
   * 失误
   */
  turnovers?: number;
  [property: string]: any;
}

// const getPlayerStat = async () => {
//   let res = await getPlayerStatApi({ matchId: props.matchId })
//   // awayList.value = res.data.awayList
//   // homeList.value = res.data.homeList
// }
const awayList = ref<MatchBasketStatPlayerDetailRes[]>([])
const homeList = ref<MatchBasketStatPlayerDetailRes[]>([])
let intervalId = ref<any>(null)
const interGetPlayer = () => {
  intervalId.value = setInterval(async () => {
    await getPlayer()
  }, 60000)
}
const getPlayer = async () => {
  let res = await getPlayerApi({ matchId: props.matchId })
  awayList.value = res.data.awayList
  homeList.value = res.data.homeList
}
getPlayer()
interGetPlayer()

// 计算主队合计数据
const homeTeamTotals = computed(() => {
  const players = homeList.value
  if (!players || players.length === 0) {
    return {
      totalMinutes: '-',
      totalPoints: 0,
      totalRebounds: 0,
      totalAssists: 0,
      totalFieldGoals: '0-0',
      totalThreePointers: '0-0',
      totalFreeThrows: '0-0',
      totalSteals: 0,
      totalBlocks: 0,
      totalTurnovers: 0,
      totalFouls: 0,
      totalOffensiveRebounds: 0,
      totalDefensiveRebounds: 0,
      goalsRate: '0%',//投篮命中率
      threePointRate: '0%',//三分命中率
      freeThrowRate: '0%'//罚球命中率
    }
  }

  return {
    totalMinutes: '-',
    totalPoints: players.reduce((sum, player) => sum + (player.points || 0), 0),
    totalRebounds: players.reduce((sum, player) => sum + (player.rebounds || 0), 0),
    totalAssists: players.reduce((sum, player) => sum + (player.assists || 0), 0),
    totalFieldGoals: `${players.reduce((sum, player) => sum + (player.fieldGoalsScored || 0), 0)}-${players.reduce((sum, player) => sum + (player.fieldGoalsTotal || 0), 0)}`,
    goalsRate: (players.reduce((sum, player) => sum + (player.fieldGoalsScored || 0), 0) / players.reduce((sum, player) => sum + (player.fieldGoalsTotal || 0), 0) * 100).toFixed(1) + '%',
    threePointRate: (players.reduce((sum, player) => sum + (player.threePointersScored || 0), 0) / players.reduce((sum, player) => sum + (player.threePointersTotal || 0), 0) * 100).toFixed(1) + '%',
    freeThrowRate: (players.reduce((sum, player) => sum + (player.freeThrowsScored || 0), 0) / players.reduce((sum, player) => sum + (player.freeThrowsTotal || 0), 0) * 100).toFixed(1) + '%',
    totalThreePointers: `${players.reduce((sum, player) => sum + (player.threePointersScored || 0), 0)}-${players.reduce((sum, player) => sum + (player.threePointersTotal || 0), 0)}`,
    totalFreeThrows: `${players.reduce((sum, player) => sum + (player.freeThrowsScored || 0), 0)}-${players.reduce((sum, player) => sum + (player.freeThrowsTotal || 0), 0)}`,
    totalSteals: players.reduce((sum, player) => sum + (player.steals || 0), 0),
    totalBlocks: players.reduce((sum, player) => sum + (player.blocks || 0), 0),
    totalTurnovers: players.reduce((sum, player) => sum + (player.turnovers || 0), 0),
    totalFouls: players.reduce((sum, player) => sum + (player.foul || 0), 0),
    totalOffensiveRebounds: players.reduce((sum, player) => sum + (player.offensiveRebounds || 0), 0),
    totalDefensiveRebounds: players.reduce((sum, player) => sum + (player.defensiveRebounds || 0), 0)
  }
})

// 计算客队合计数据
const awayTeamTotals = computed(() => {
  const players = awayList.value
  if (!players || players.length === 0) {
    return {
      totalMinutes: '-',
      totalPoints: 0,
      totalRebounds: 0,
      totalAssists: 0,
      totalFieldGoals: '0-0',
      totalThreePointers: '0-0',
      totalFreeThrows: '0-0',
      totalSteals: 0,
      totalBlocks: 0,
      totalTurnovers: 0,
      totalFouls: 0,
      totalOffensiveRebounds: 0,
      totalDefensiveRebounds: 0,
      goalsRate: '0%',//投篮命中率
      threePointRate: '0%',//三分命中率
      freeThrowRate: '0%'//罚球命中率
    }
  }

  return {
    totalMinutes: '-',
    totalPoints: players.reduce((sum, player) => sum + (player.points || 0), 0),
    totalRebounds: players.reduce((sum, player) => sum + (player.rebounds || 0), 0),
    totalAssists: players.reduce((sum, player) => sum + (player.assists || 0), 0),
    goalsRate: (players.reduce((sum, player) => sum + (player.fieldGoalsScored || 0), 0) / players.reduce((sum, player) => sum + (player.fieldGoalsTotal || 0), 0) * 100).toFixed(1) + '%',
    threePointRate: (players.reduce((sum, player) => sum + (player.threePointersScored || 0), 0) / players.reduce((sum, player) => sum + (player.threePointersTotal || 0), 0) * 100).toFixed(1) + '%',
    freeThrowRate: (players.reduce((sum, player) => sum + (player.freeThrowsScored || 0), 0) / players.reduce((sum, player) => sum + (player.freeThrowsTotal || 0), 0) * 100).toFixed(1) + '%',
    totalFieldGoals: `${players.reduce((sum, player) => sum + (player.fieldGoalsScored || 0), 0)}-${players.reduce((sum, player) => sum + (player.fieldGoalsTotal || 0), 0)}`,
    totalThreePointers: `${players.reduce((sum, player) => sum + (player.threePointersScored || 0), 0)}-${players.reduce((sum, player) => sum + (player.threePointersTotal || 0), 0)}`,
    totalFreeThrows: `${players.reduce((sum, player) => sum + (player.freeThrowsScored || 0), 0)}-${players.reduce((sum, player) => sum + (player.freeThrowsTotal || 0), 0)}`,
    totalSteals: players.reduce((sum, player) => sum + (player.steals || 0), 0),
    totalBlocks: players.reduce((sum, player) => sum + (player.blocks || 0), 0),
    totalTurnovers: players.reduce((sum, player) => sum + (player.turnovers || 0), 0),
    totalFouls: players.reduce((sum, player) => sum + (player.foul || 0), 0),
    totalOffensiveRebounds: players.reduce((sum, player) => sum + (player.offensiveRebounds || 0), 0),
    totalDefensiveRebounds: players.reduce((sum, player) => sum + (player.defensiveRebounds || 0), 0)
  }
})

// 计算各项最高统计的辅助函数
const getTopPlayerStat = (players: MatchBasketStatPlayerDetailRes[], statKey: string, isTimeFormat = false) => {
  if (!players || players.length === 0) {
    return { display: '-', value: 0, isHighest: false }
  }

  let maxValue = 0
  let topPlayer: MatchBasketStatPlayerDetailRes = {}

  players.forEach(player => {
    let value = 0

    if (isTimeFormat && player.duration) {
      // 直接使用duration进行比较，相同时取第一个
      value = parseFloat(player.duration.replace(':', '.')) || 0
    } else {
      value = (player as any)[statKey] || 0
    }

    if (value > maxValue) {
      maxValue = value
      topPlayer = player
    }
  })

  if (!topPlayer) {
    return { display: '-', value: 0, isHighest: false }
  }

  let displayValue = ''
  if (isTimeFormat) {
    displayValue = `${topPlayer.playerName || '-'}${topPlayer.duration || ''}`
  } else {
    displayValue = `${topPlayer.playerName || '-'}${(topPlayer as any)[statKey] || ''}`
  }

  return {
    display: displayValue,
    value: maxValue,
    isHighest: false // 这个会在后面的比较中设置
  }
}

// 计算主队各项最高统计
const homeTopStats = computed(() => {
  const players = homeList.value
  return {
    points: getTopPlayerStat(players, 'points'),
    rebounds: getTopPlayerStat(players, 'rebounds'),
    assists: getTopPlayerStat(players, 'assists'),
    steals: getTopPlayerStat(players, 'steals'),
    blocks: getTopPlayerStat(players, 'blocks'),
    minutes: getTopPlayerStat(players, 'duration', true),
    turnovers: getTopPlayerStat(players, 'turnovers'),
    fouls: getTopPlayerStat(players, 'foul')
  }
})

// 计算客队各项最高统计
const awayTopStats = computed(() => {
  const players = awayList.value
  return {
    points: getTopPlayerStat(players, 'points'),
    rebounds: getTopPlayerStat(players, 'rebounds'),
    assists: getTopPlayerStat(players, 'assists'),
    steals: getTopPlayerStat(players, 'steals'),
    blocks: getTopPlayerStat(players, 'blocks'),
    minutes: getTopPlayerStat(players, 'duration', true),
    turnovers: getTopPlayerStat(players, 'turnovers'),
    fouls: getTopPlayerStat(players, 'foul')
  }
})

// 计算哪一队在各项统计中更高（用于高亮显示）
const compareStats = computed(() => {
  const home = homeTopStats.value
  const away = awayTopStats.value

  // 比较各项统计，设置isHighest标志
  const stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'minutes', 'turnovers', 'fouls']

  stats.forEach(stat => {
    const homeValue = home[stat].value
    const awayValue = away[stat].value

    if (homeValue > awayValue) {
      home[stat].isHighest = true
      away[stat].isHighest = false
    } else if (awayValue > homeValue) {
      home[stat].isHighest = false
      away[stat].isHighest = true
    } else {
      // 相等时都不高亮
      home[stat].isHighest = false
      away[stat].isHighest = false
    }
  })

  return { home, away }
})

// 监听比较结果的变化
const finalHomeTopStats = computed(() => {
  compareStats.value // 触发比较计算
  return homeTopStats.value
})

const finalAwayTopStats = computed(() => {
  compareStats.value // 触发比较计算
  return awayTopStats.value
})



const positionMap = {
  F: '前锋',
  M: '中场',
  D: '后卫',
  G: '守门员',
};
const getPositionName = (position) => {
  return positionMap[position] || '未知';
};
//获取伤停
interface MatchFootInjuryDetailRes {
  id?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 球员logo
   */
  playerLogo?: string;
  /**
   * 球员名称
   */
  playerName?: string;
  /**
   * 球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知
   */
  position?: string;
  /**
   * 伤停原因
   */
  reason?: string;
  /**
   * 球员号
   */
  shirtNumber?: string;
  /**
   * 球队id
   */
  teamId?: number;
  /**
   * 1主 2客
   */
  type?: number;
  [property: string]: any;
}
// let injuryList = ref<MatchFootInjuryDetailRes[]>([])
// const getInjury = async () => {
//   let res = await getInjuryApi({ matchId: props.matchId })
//   injuryList.value = res.data
// }
// getInjury()
// 组件卸载时清理资源
onUnmounted(() => {
  clearInterval(intervalId.value)
  intervalId.value = null
});
</script>

<style scoped lang="scss">
.basketball-result {
  background-color: #f5f5f5;
}

.section {
  margin-bottom: 30px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .red-line {
    width: 4px;
    height: 20px;
    background: #EB0000;
    margin-right: 10px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}


.team-stats {
  margin-bottom: 30px;
}

.team-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  // padding: 10px 15px;

  .logo {
    width: 30px;
    height: 30px;
    display: block;
    margin-right: 10px;
  }
}

.team-name {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* .team-info {
  font-size: 12px;
  color: #666;
} */

// 球员统计表格样式
.player-table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  // border: 1px solid #e8e8e8;
}

.player-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  min-width: 800px;

  .table-header {
    background-color: #fafafa;

    th {
      font-weight: 600;
      color: #333;
      text-align: center;
      padding: 12px 8px;
      border: none;
      border-bottom: 1px solid #e8e8e8;
      font-size: 14px;
      white-space: nowrap;

      &.player-col {
        text-align: left;
        min-width: 80px;
      }

      &.time-col {
        min-width: 60px;
      }

      &.stat-col {
        min-width: 50px;
      }
    }
  }

  tbody {
    tr {
      &.starter-row {
        background-color: #FFDBD9;
      }

      &.bench-row {
        background-color: #ffffff;
      }

      &.total-row {
        background-color: #f0f2f5;
        font-weight: bold;
        border-top: 1px solid #d9d9d9;
      }

      td {
        text-align: center;
        padding: 10px 8px;
        border: none;
        border-bottom: 1px solid #f0f0f0;
        font-size: 13px;

        &.player-name {
          text-align: left;
          font-weight: 500;
          color: #333;
        }

        &.stat-data {
          color: #666;
        }

        &.points-data {
          font-weight: bold;
          // color: #EB0000;
        }

        &.plus-minus-data {
          font-weight: 500;

          &.positive {
            color: #52c41a;
          }

          &.negative {
            color: #ff4d4f;
          }
        }

        &.total-label {
          text-align: left;
          font-weight: 600;
        }

        &.total-data {
          font-weight: 600;
          color: #333;
        }
      }
    }
  }
}

// 球队统计表格样式
.team-stats-table {
  width: 100%;
}

.team-comparison-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;

  .table-header {
    background-color: #F4F4F4;

    th {
      text-align: center;
      font-weight: 600;
      color: #333;
      padding: 10px 12px;
      border: none;
      border-bottom: 1px solid #e8e8e8;
      font-size: 14px;

      &.team-col {
        text-align: left;
        min-width: 100px;
      }

      &.stat-col {
        min-width: 80px;
      }
    }
  }

  tbody {
    .team-row {
      border-bottom: 1px solid #f0f0f0;

      &:nth-child(even) {
        background-color: #f8f9fa;
      }

      &:nth-child(odd) {
        background-color: #ffffff;
      }

      td {
        text-align: center;
        padding: 15px 12px;
        border: none;
        font-size: 14px;

        &.team-name {
          text-align: left;
          font-weight: 600;
          color: #333;
        }

        &.stat-value {
          font-weight: 500;
        }
      }
    }
  }
}

// 详细统计表格样式
.detailed-stats {
  width: 100%;
}

.detailed-stats-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;

  .table-header {
    background-color: #F4F4F4;

    th {
      text-align: center;
      font-weight: 600;
      color: #333;
      padding: 10px 8px;
      border: none;
      border-bottom: 1px solid #e8e8e8;
      font-size: 14px;

      &.team-col {
        text-align: left;
        min-width: 100px;
      }

      &.stat-col {
        min-width: 80px;
      }
    }
  }

  tbody {
    .team-row {
      &:nth-child(even) {
        background-color: #f8f9fa;
      }

      &:nth-child(odd) {
        background-color: #ffffff;
      }

      td {
        text-align: center;
        padding: 15px 8px;
        border: none;
        border-bottom: 1px solid #f0f0f0;
        font-size: 14px;

        &.team-name {
          text-align: left;
          font-weight: 600;
          color: #333;
        }

        &.stat-value {
          font-weight: 500;
          color: #666;
        }
      }
    }
  }
}

// 伤停情况样式
.injury-section {
  display: flex;
  gap: 20px;

  .injury-table {
    flex: 1;
    border: 1px solid #E8E8E8;
    border-radius: 8px;
    padding: 15px;

    .injury-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .team-logo {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }

      .team-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .injury-list {
      .injury-header-row {
        display: grid;
        grid-template-columns: 1fr 80px 100px;
        gap: 15px;
        padding: 8px 0;
        background: #F5F5F5;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;

        span {
          text-align: center;
          font-size: 12px;

          &:first-child {
            text-align: left;
          }
        }
      }

      .injury-row {
        display: grid;
        grid-template-columns: 1fr 80px 100px;
        gap: 15px;
        padding: 8px 0;
        border-bottom: 1px solid #F0F0F0;
        align-items: center;

        &:last-child {
          border-bottom: none;
        }

        .player-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .player-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
          }

          .player-name {
            font-size: 12px;
            color: #333;
          }
        }

        .position,
        .reason {
          text-align: center;
          font-size: 12px;
          color: #333;
          margin-right: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .player-table-container {
    overflow-x: auto;
  }

  .player-table {
    min-width: 900px;

    .table-header th,
    tbody td {
      padding: 8px 4px;
      font-size: 12px;
    }
  }

  .team-comparison-table {

    .table-header th,
    tbody td {
      padding: 10px 6px;
      font-size: 12px;
    }
  }

  .detailed-stats-table {

    .table-header th,
    tbody td {
      padding: 8px;
      font-size: 12px;
    }
  }

  .injury-section {
    flex-direction: column;
    gap: 20px;
  }

  .team-header {
    flex-direction: column;
    gap: 10px;
  }
}
</style>