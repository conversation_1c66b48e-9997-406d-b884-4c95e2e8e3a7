<template>
  <div class="none-box">
    <img v-if="props.isIocn" :src="actualImageSrc" :alt="text" class="none-image" :style="imageStyle" />
    <p class="none-text" :style="textStyle">{{ text }}</p>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import defaultImage from '@/static/details/event-line-none.png'

// 定义组件属性
const props = defineProps({
  // 提示文字
  text: {
    type: String,
    default: '暂无数据'
  },
  // 图片路径
  imageSrc: {
    type: String,
    default: ''
  },
  // 图片大小
  imageSize: {
    type: [String, Number],
    default: 240
  },
  // 文字颜色
  textColor: {
    type: String,
    default: '#999'
  },
  // 文字大小
  textSize: {
    type: [String, Number],
    default: 18
  },
  isIocn: {
    type: Boolean,
    default: true
  }
})

// 计算实际使用的图片路径
const actualImageSrc = computed(() => {
  return props.imageSrc || defaultImage
})

// 计算样式
const imageStyle = computed(() => ({
  width: typeof props.imageSize === 'number' ? `${props.imageSize}px` : props.imageSize,
  height: typeof props.imageSize === 'number' ? `${props.imageSize}px` : props.imageSize
}))

const textStyle = computed(() => ({
  color: props.textColor,
  fontSize: typeof props.textSize === 'number' ? `${props.textSize}px` : props.textSize
}))
</script>

<style scoped>
.none-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.none-image {
  display: block;
  margin-bottom: 16px;
  opacity: 0.6;
}

.none-text {
  margin: 0;
  line-height: 1.5;
}
</style>