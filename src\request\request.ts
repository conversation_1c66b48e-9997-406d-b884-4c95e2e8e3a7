import axios from 'axios'
import type { MessageReactive } from 'naive-ui'
import { signRequest } from '@/utils/sign'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL
})
let msgReactive: MessageReactive | null = null

// 创建一个回调函数来处理401错误，由应用程序设置
let handle401Callback: (() => void) | null = null

// 导出设置401处理回调的函数
export const setHandle401Callback = (callback: () => void) => {
  handle401Callback = callback
}

// request interceptor
request.interceptors.request.use(
  (config: any) => {
    config.data = config.data || {}
    config = signRequest(config)
    return config
  },
  (error) => {
    // do something with request error
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  async (res) => {
    if (res.data.code === 401) {
      // 调用外部设置的401处理回调
      if (handle401Callback) {
        handle401Callback()
      }
    } else if (res.data.code !== 0) {
      msgReactive = window.$message.error(res.data.message || '系统错误')
    }
    return res.data.code === 0 ? res.data : Promise.reject(res.data)
  },
  (error) => {
    // const msg: string = error.msg || '系统错误！'
    // ElMessage.error(msg)
    return Promise.reject(error.response)
  }
)

export default request
