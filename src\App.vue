<script setup lang="ts">
import { RouterView } from 'vue-router'
import { NConfigProvider } from 'naive-ui'
import { zhCN, dateZhCN } from 'naive-ui'
// import { NThemeEditor } from 'naive-ui'
import themeOverrides from '@/common/theme'
// 引入上面你所定义的全局挂载组件
import GlobalView from '@/components/global/GlobalView.vue'
import AuthModal from '@/components/auth/AuthModal.vue'

import { useAuthStore } from '@/stores/auth'
import { computed } from 'vue'

// 使用认证store
const authStore = useAuthStore()

// 计算属性，响应式地获取弹窗状态
const showAuthModal = computed({
  get: () => authStore.showAuthModal,
  set: (value) => {
    if (!value) {
      authStore.hideAuthModal()
    }
  }
})

const authModalType = computed(() => authStore.authModalType)
</script>

<template>
  <n-config-provider :locale="zhCN" :date-locale="dateZhCN" :theme-overrides="themeOverrides">
    <n-message-provider>
      <GlobalView></GlobalView>
      <RouterView />
      <AuthModal v-model:show="showAuthModal" :type="authModalType" />
    </n-message-provider>
  </n-config-provider>
</template>

<style lang="scss">
/* @media (min-width: 1024px) {
} */
html,
body,
#app {
  width: 100%;
  height: 100%;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-corner {
  display: block;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  cursor: pointer;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb,
::-webkit-scrollbar-track {
  border-right-color: transparent;
  border-left-color: transparent;
  background-color: rgba(0, 0, 0, 0.1);
}

.dui-scrollbar {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.dui-scrollbar__container {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
  scrollbar-width: none;
  cursor: default;
}

.dui-scrollbar__container::-webkit-scrollbar {
  width: 0;
  height: 0;
}

// .scrollbar-box::-webkit-scrollbar {
//   width: calc(var(--scrollbar-width-regular) + 4px * 2);
//   height: calc(var(--scrollbar-width-regular) + 4px * 2);
//   background: transparent;
// }
// .scrollbar-box--safari::-webkit-scrollbar {
//   width: 0 !important;
// }
// .scrollbar-box:hover::-webkit-scrollbar-thumb {
//   background: var(--scrollbar-color-regular);
// }
// .scrollbar-box::-webkit-scrollbar-thumb {
//   background-clip: padding-box !important;
// }
// .scrollbar-box::-webkit-scrollbar-thumb {
//   border: 4px solid transparent;
//   border-radius: calc(var(--scrollbar-width-regular) + 4px * 2);
// }
// .scrollbar-box::-webkit-scrollbar-thumb:hover {
//   background: var(--scrollbar-color-hover);
// }</style>
