<template>
  <div class="modify-phone">
    <div class="phone-header">
      <img class="back-img" src="@/static/user/left-arrow-icon.png" @click="handleBack" />
      <h3>改绑手机</h3>
    </div>
    <!-- 修改手机号步骤条 -->
    <div class="phone-step">
      <div class="step-box">
        <div v-if="step === 1" class="icon"></div>
        <div class="label" :class="{ active: step === 1 }">验证身份</div>
      </div>
      <div class="dash-line"></div>
      <div class="step-box">
        <div v-if="step === 2" class="icon"></div>
        <div class="label" :class="{ active: step === 2 }">改绑新手机</div>
      </div>
      <div class="dash-line"></div>
      <div class="step-box">
        <div v-if="step === 3" class="icon"></div>
        <div class="label" :class="{ active: step === 3 }">修改完成</div>
      </div>
    </div>
    <template v-if="step === 1">
      <n-form class="modify-phone-form" ref="stepOneFormRef" :model="stepOneFormValue" :show-label="false"
        :rules="stepOneRules">
        <!-- 手机号输入框 -->
        <n-form-item>
          <n-input v-model:value="maskedPhone" placeholder="请输入手机号码" size="large" disabled />
        </n-form-item>
        <!-- 验证码输入框 -->
        <n-form-item path="verifyCode">
          <n-input v-model:value="stepOneFormValue.verifyCode" placeholder="请输入验证码" size="large" clearable>
            <template #suffix>
              <n-button text :disabled="countdown > 0" @click="sendVerifyCode" class="verify-btn">
                {{ countdown > 0 ? `${countdown}s后重发` : '获取验证码' }}
              </n-button>
            </template>
          </n-input>
        </n-form-item>
        <!-- 下一步按钮 -->
        <div class="next-step-box">
          <button class="next-step-btn" @click="handleNextStep">
            下一步
          </button>
        </div>
      </n-form>
    </template>
    <template v-else>
      <n-form class="modify-phone-form" ref="stepTwoFormRef" :model="stepTwoFormValue" :show-label="false"
        :rules="stepTwoRules">
        <!-- 手机号输入框 -->
        <n-form-item path="phoneNumber">
          <n-input v-model:value="stepTwoFormValue.phoneNumber" placeholder="请输入手机号码" size="large" clearable />
        </n-form-item>
        <!-- 验证码输入框 -->
        <n-form-item path="newVerifyCode">
          <n-input v-model:value="stepTwoFormValue.newVerifyCode" placeholder="请输入验证码" size="large" clearable>
            <template #suffix>
              <n-button text :disabled="newCountdown > 0" @click="sendNewVerifyCode" class="verify-btn">
                {{ newCountdown > 0 ? `${newCountdown}s后重发` : '获取验证码' }}
              </n-button>
            </template>
          </n-input>
        </n-form-item>
        <!-- 完成按钮 -->
        <div class="next-step-box">
          <button class="next-step-btn" @click="handleFinished">
            完成
          </button>
        </div>
      </n-form>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, type FormInst, type FormItemRule } from 'naive-ui'
import { useUserStore } from '@/stores/user'
import { maskPhoneNumber } from '@/utils/common'
import { getSmsCodeApi, checkCodeApi, updateUserPhoneApi } from '@/api/user'
import { validatePhone, validateVerifyCode } from '@/utils/validate'

const router = useRouter()
const message = useMessage()
const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

// 格式化手机号
const maskedPhone = computed(() => {
  return maskPhoneNumber(userInfo.value.phoneNum)
})

// 响应式数据
const stepOneFormValue = reactive({
  verifyCode: '' // 旧手机号验证码
})

const stepTwoFormValue = reactive({
  phoneNumber: '', // 新手机号
  newVerifyCode: '' // 新手机号验证码
})
// 令牌
const uuid = ref('')
const countdown = ref(0)
const newCountdown = ref(0)
// 步骤
const step = ref(1)
const stepOneFormRef = ref<FormInst | null>(null)
const stepTwoFormRef = ref<FormInst | null>(null)

// 步骤一表单校验规则
const stepOneRules = {
  verifyCode: [
    { required: true, validator: validateVerifyCode, trigger: ['blur', 'input'] }
  ]
}

// 步骤二表单校验规则
const stepTwoRules = {
  phoneNumber: [
    { required: true, validator: validatePhone, trigger: ['blur', 'input'] }
  ],
  newVerifyCode: [
    { required: true, validator: validateVerifyCode, trigger: ['blur', 'input'] }
  ]
}

// 获取验证码
const sendVerifyCode = async () => {
  try {
    await getSmsCodeApi({
      phone: userInfo.value.phoneNum,
      businessType: 'MSG_UPDATE_PHONE'
    })
    message.success('验证码已发送')
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    message.error('验证码发送失败')
  }
}

// 获取验证码
const sendNewVerifyCode = async () => {
  if (!stepTwoFormValue.phoneNumber) {
    message.warning('请先输入手机号码')
    return
  }
  try {
    await getSmsCodeApi({
      phone: stepTwoFormValue.phoneNumber,
      businessType: 'MSG_UPDATE_PHONE'
    })
    message.success('验证码已发送')
    // 开始倒计时
    newCountdown.value = 60
    const timer = setInterval(() => {
      newCountdown.value--
      if (newCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    message.error('验证码发送失败')
  }
}

// 下一步
const handleNextStep = async (e: MouseEvent) => {
  e.preventDefault()
  stepOneFormRef.value?.validate(async (errors) => {
    if (!errors) {
      const { data } = await checkCodeApi({
        phone: userInfo.value.phoneNum,
        smsCode: stepOneFormValue.verifyCode,
        businessType: 'MSG_UPDATE_PHONE'
      })
      uuid.value = data
      step.value = 2
    }
  })
}

// 完成
const handleFinished = (e: MouseEvent) => {
  e.preventDefault()
  stepTwoFormRef.value?.validate(async (errors) => {
    if (!errors) {
      await updateUserPhoneApi({
        phone: stepTwoFormValue.phoneNumber,
        smsCode: stepTwoFormValue.newVerifyCode,
        uuid: uuid.value
      })
      message.success('修改手机号成功')
      await userStore.loginOut()
      router.push('/')
    }
  })
}

const handleBack = () => {
  if (step.value === 2) {
    step.value = 1
  } else {
    router.back()
  }
}
</script>

<style lang="scss" scoped>
.modify-phone {
  background-color: #fff;
  min-height: 100%;

  .phone-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .back-img {
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .phone-step {
    display: flex;
    justify-content: space-around;
    margin: 0 40px;
    height: 60px;
    background: #F6F6F6;
    padding: 0 80px;

    .step-box {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #818181;
      line-height: 16px;

      .icon {
        width: 10px;
        height: 10px;
        background: #FB2B1F;
        border-radius: 50%;
        margin-right: 6px;
      }

      .active {
        font-weight: 600;
        color: #333333;
      }
    }

    .dash-line {
      flex: 1;
      height: 1px;
      border-top: 1px dashed #818181;
      margin: 30px 15px;
    }
  }

  .modify-phone-form {
    padding: 40px;

    // .form-item {
    // margin-bottom: 20px;

    .verify-btn {
      color: #FB2B1F;
      font-size: 14px;

      &:disabled {
        color: #cccccc;
      }
    }

    // }

    .next-step-box {
      text-align: center;
      margin-top: 40px;

      .next-step-btn {
        width: 100%;
        max-width: 400px;
        padding: 12px 24px;
        background: #FB2B1F;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: #E02419;
        }

        &:disabled {
          background: #CCCCCC;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>