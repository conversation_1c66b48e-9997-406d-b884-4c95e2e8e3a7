<template>
  <div class="live-appointments">
    <div class="page-header">
      <div class="line"></div>
      <h3>预约直播</h3>
    </div>
    <div class="page-content">
      <!-- 游戏类型标签 -->
      <div class="game-types">
        <div class="game-tag" :class="{ active: selectedGame === game.value }" v-for="game in gameTypes"
          :key="game.value" @click="selectGame(game.value)">
          {{ game.label }}
        </div>
      </div>
      <!-- 日期选择 -->
      <div class="date-selector">
        <div class="date-item" :class="{ active: selectedDate === date.value }" v-for="date in dateList"
          :key="date.value" @click="selectDate(date.value)">
          <div class="date-label">{{ date.label }}</div>
          <div class="date-value">{{ date.value }}</div>
        </div>
      </div>
      <!-- 比赛列表 -->
      <div class="match-list">
        <!-- 表头 -->
        <div class="list-header">
          <div class="col-event">赛事</div>
          <div class="col-time">比赛时间</div>
          <div class="col-match">对阵</div>
          <div class="col-action">操作</div>
        </div>
        <!-- 比赛项目 -->
        <div class="match-container" v-if="matchList.length > 0">
          <div class="match-item" v-for="match in matchList" :key="match.id">
            <div class="col-event">{{ match.sclassName }}</div>
            <div class="col-time">{{ dayjs(match.matchTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div class="col-match">
              <div class="team-vs" v-if="selectedGame == 'basket'">
                <div class="team">
                  <!-- <img :src="match.awayLogo" :alt="match.awayName" class="team-logo"> -->
                  <defaultLogo class="logo" type="team" :logo="match.awayLogo" width="20px" height="20px"></defaultLogo>
                  <span class="team-name">{{ match.awayName }}</span>
                </div>
                <span class="vs">VS</span>
                <div class="team">
                  <!-- <img :src="match.homeLogo" :alt="match.homeName" class="team-logo"> -->
                  <defaultLogo class="logo" type="team" :logo="match.homeLogo" width="20px" height="20px"></defaultLogo>
                  <span class="team-name">{{ match.homeName }}</span>
                </div>
              </div>
              <div class="team-vs" v-else>
                <div class="team">
                  <!-- <img :src="match.homeLogo" :alt="match.homeName" class="team-logo"> -->
                  <defaultLogo class="logo" type="team" :logo="match.homeLogo" width="20px" height="20px"></defaultLogo>
                  <span class="team-name">{{ match.homeName }}</span>
                </div>
                <span class="vs">VS</span>
                <div class="team">
                  <!-- <img :src="match.awayLogo" :alt="match.awayName" class="team-logo"> -->
                  <defaultLogo class="logo" type="team" :logo="match.awayLogo" width="20px" height="20px"></defaultLogo>
                  <span class="team-name">{{ match.awayName }}</span>
                </div>
              </div>
            </div>
            <div class="col-action">
              <button class="action-btn" :class="getActionButtonClass(match.auditState)"
                @click="handleReserveMatch(match.id)" :disabled="[0, 1, 2, 3, 4].includes(match.auditState)">
                {{ getActionButtonText(match.auditState) }}
              </button>
            </div>
          </div>
        </div>
        <div v-else class="none-box">
          <none-box text="暂无赛事" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'
import { useMessage } from 'naive-ui'
import { getMatchPageApi, reserveMatchLiveApi } from '@/api/user'

const message = useMessage()

interface GameType {
  label: string
  value: string
}

interface DateItem {
  label: string
  value: string
  apiDate: string
}

interface Match {
  id: number
  sclassName: string
  matchTime: string
  homeName: string
  homeLogo: string
  awayName: string
  awayLogo: string
  auditState: 0 | 1 | 2 | 3 | 4 // 0待审核, 1审核通过, 2审核不通过 3.禁播 4.删除 null代表没预约
}

const gameTypes: GameType[] = [
  { label: '足球', value: 'foot' },
  { label: '篮球', value: 'basket' },
  { label: '英雄联盟', value: 'lol' },
  { label: 'DOTA2', value: 'dota' },
  { label: 'CS:GO', value: 'csgo' },
  { label: '王者荣耀', value: 'hok' }
]

// 动态生成日期列表
const generateDateList = () => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const today = dayjs()
  const dateList: DateItem[] = []
  for (let i = 0; i < 7; i++) {
    const currentDate = today.add(i, 'day')
    dateList.push({
      label: i === 0 ? '今天' : weekdays[currentDate.day()], // 今天/周几
      value: currentDate.format('MM.DD'), // 格式化为 月.日
      apiDate: currentDate.format('YYYY-MM-DD') // 接口用 年月日
    })
  }
  return dateList
}

// 初始化日期列表和默认选中日期
const dateList = ref<DateItem[]>(generateDateList())
const selectedDate = ref<string>(dateList.value[0].value)

const selectedGame = ref<string>('foot')
// 选择日期
const selectDate = (date: string) => {
  selectedDate.value = date
  getMatchList()
}

// 选择游戏类型
const selectGame = (gameType: string) => {
  selectedGame.value = gameType
  getMatchList()
}

// 计算属性：根据选择的游戏类型和日期过滤比赛
// const filteredMatches = computed(() => {
//   return matchList.value.filter(match =>
//     match.gameType === selectedGame.value && match.date === selectedDate.value
//   )
// })

// 获取操作按钮的样式类
const getActionButtonClass = (status: number) => {
  switch (status) {
    case 0:
      return 'btn-pending'
    case 1:
      return 'btn-success'
    case 2:
      return 'btn-danger'
    default:
      return 'btn-primary'
  }
}

// 获取操作按钮的文本
const getActionButtonText = (status: number) => {
  switch (status) {
    case 0:
      return '待审核'
    case 1:
      return '预约成功'
    case 2:
      return '预约失败'
    default:
      return '预约直播'
  }
}

// 预约直播
const handleReserveMatch = async (matchId: number) => {
  await reserveMatchLiveApi({
    matchId,
    liveType: selectedGame.value
  })
  // 显示消息前先清除所有
  message.destroyAll()
  message.success('预约直播成功')
  getMatchList()
}

const matchList = ref<Match[]>([])
// 获取赛程列表
const getMatchList = async () => {
  const selectedApiDate = dateList.value.find(date => date.value === selectedDate.value)?.apiDate
  if (!selectedApiDate) return
  const { data } = await getMatchPageApi({
    date: selectedApiDate,
    liveTypeEnum: selectedGame.value
  })
  matchList.value = data
}

onMounted(() => {
  getMatchList()
})
</script>

<style lang="scss" scoped>
.live-appointments {
  background-color: #fff;
  min-height: 100%;

  .page-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .page-content {
    padding: 0 36px 40px;

    .game-types {
      display: flex;
      gap: 16px;
      // margin-bottom: 32px;
      flex-wrap: wrap;
      width: fit-content;
      background: #F3F3F3;
      border-radius: 16px;
      padding: 8px 16px;
      margin: 0 auto;

      .game-tag {
        padding: 4px 12px;
        border-radius: 4px;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          color: #FB2B1F;
        }

        &.active {
          font-weight: 600;
          background: #fff;
          color: #FB2B1F;
        }
      }
    }

    .date-selector {
      display: flex;
      gap: 16px;
      margin-top: 20px;
      margin-bottom: 20px;
      flex-wrap: wrap;
      justify-content: center;

      .date-item {
        padding: 10px 16px;
        border-bottom: 1px solid #E5E5E5;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fff;
        text-align: center;
        min-width: 80px;

        &:hover {
          border-color: #FB2B1F;
        }

        &.active {
          border-color: #FB2B1F;
          background: #FFF5F5;

          .date-label {
            color: #FB2B1F;
            font-weight: 600;
          }

          .date-value {
            color: #FB2B1F;
            font-weight: 500;
          }
        }

        .date-label {
          font-size: 16px;
          color: #666;
          margin-bottom: 4px;
        }

        .date-value {
          font-size: 14px;
          color: #333;
        }
      }
    }

    .match-list {

      .list-header {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        background: #F8F8F8;
        border-radius: 4px 4px 0 0;
        font-weight: 500;
        color: #333;
        font-size: 14px;

        .col-event {
          width: 100px;
          text-align: center;
        }

        .col-time {
          width: 160px;
          text-align: center;
        }

        .col-match {
          flex: 1;
          text-align: center;
        }

        .col-action {
          width: 100px;
          text-align: center;
        }
      }

      .match-container {
        overflow-y: auto;
        // height: calc(100vh - 500px);

        .match-item {
          display: flex;
          align-items: center;
          padding: 20px;
          border-top: none;
          background: #fff;
          transition: all 0.3s ease;

          &:hover {
            background: #FAFAFA;
          }

          &:last-child {
            border-radius: 0 0 4px 4px;
          }

          .col-event {
            width: 100px;
            font-size: 14px;
            color: #333;
            text-align: center;
          }

          .col-time {
            width: 160px;
            font-size: 14px;
            color: #666;
            text-align: center;
          }

          .col-match {
            flex: 1;

            .team-vs {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 16px;

              .team {
                display: flex;
                align-items: center;
                gap: 8px;

                .team-logo {
                  width: 20px;
                  height: 20px;
                  border-radius: 50%;
                }

                .team-name {
                  font-size: 14px;
                  color: #333;
                }
              }

              .vs {
                font-size: 12px;
                color: #999;
              }
            }
          }

          .col-action {
            width: 100px;
            text-align: center;

            .action-btn {
              padding: 6px 16px;
              border: none;
              border-radius: 4px;
              font-size: 12px;
              cursor: pointer;
              transition: all 0.3s ease;

              &.btn-success {
                background: #52C41A;
                color: white;
                cursor: not-allowed;

                &:hover {
                  background: #389E0D;
                }
              }

              &.btn-danger {
                background: #FB2B1F;
                color: white;
                cursor: not-allowed;

                &:hover {
                  background: #E02419;
                }
              }

              &.btn-primary {
                background: #1890FF;
                color: white;

                &:hover {
                  background: #096DD9;
                }
              }

              &.btn-pending {
                background: #D9D9D9;
                color: #666;
                cursor: not-allowed;
              }
            }
          }
        }
      }
    }
  }
}
</style>
