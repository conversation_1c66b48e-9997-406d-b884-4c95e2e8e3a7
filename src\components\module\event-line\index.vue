<template>
  <div class="incidents-box">
    <template v-if="filteredIncidents.length > 0">
      <div class="just-goal">
        <span class="t">只看进球</span>
        <n-checkbox size="large" v-model:checked="isGoal" :checked-value="true" />
      </div>
      <div class="status-title">比赛开始</div>
      <div class="sub-box">
        <div class="ind-item" v-for="item in filteredIncidents" :key="item.id">
          <div class="ind-sub-item home-container" v-if="item.position === 1">
            <div class="home-item">
              <div class="event-item" v-if="item.incidentType === 1">
                <div class="event-sub-item ">
                  <div>{{ item.homeScore || 0 }}-{{ item.awayScore || 0 }}</div>
                </div>
                <div class="event-sub-item">
                  <div class="t">{{ item.playerName || '' }}</div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
                <div class="event-sub-item" v-if="item.assist1Name">
                  <div class="t">{{ item.assist1Name || '' }}</div>
                  <details-icons class="icon" type="ASSIST"></details-icons>
                </div>
              </div>
              <div class="event-item"
                v-if="item.incidentType === 2 || item.incidentType === 3 || item.incidentType === 4">
                <div class="event-sub-item">
                  <div class="t">{{ item.reason || '' }} {{ item.playerName || '' }}</div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 9">
                <div class="event-sub-item">
                  <div class="sub-info">
                    <div>{{ item.playerName }}</div>
                    <div>{{ item.outPlayerName }}</div>
                  </div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 8">
                <div class="event-sub-item">
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div>{{ item.playerName }}</div>
                  </div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 15">
                <div class="event-sub-item">
                  <div class="t">{{ item.reason }} {{ item.playerName }}</div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 16">
                <div class="event-sub-item">
                  <div class="t">{{ item.reason }} {{ item.playerName }}</div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 17">
                <div class="event-sub-item">
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div class="t">{{ item.reason }} {{ item.playerName }}</div>
                  </div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 28">
                <div class="event-sub-item">
                  <div class="sub-info">
                    <!-- <div v-if="item.varResult <= 4">{{ item.homeScore }}-{{ item.awayScore }}</div> -->
                    <div>{{ item.playerName }}</div>
                    <div>{{ varType[item.varResult]?.text || '未知' }}</div>
                  </div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 29">
                <div class="event-sub-item">
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div>{{ item.playerName }}</div>
                  </div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 30">
                <div class="event-sub-item">
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div>{{ item.playerName }}</div>
                  </div>
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                </div>
              </div>
            </div>
            <div class="time" v-if="item.incidentType === 29 || item.incidentType === 30">点</div>
            <div class="time" v-else>{{ item.time }}'</div>
          </div>
          <div class="ind-sub-item away-container" v-if="item.position === 2">
            <div class="away-item">
              <div class="event-item" v-if="item.incidentType === 1">
                <div class="event-sub-item ">
                  <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                </div>
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="t">{{ item.playerName }}</div>
                </div>
                <div class="event-sub-item" v-if="item.assist1Name">
                  <details-icons class="icon" type="ASSIST"></details-icons>
                  <div class="t">{{ item.assist1Name }}</div>
                </div>
              </div>
              <div class="event-item"
                v-if="item.incidentType === 2 || item.incidentType === 3 || item.incidentType === 4">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="t">{{ item.playerName }} {{ item.reason }}</div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 9">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="sub-info">
                    <div>{{ item.playerName }}</div>
                    <div>{{ item.outPlayerName }}</div>
                  </div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 8">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div>{{ item.playerName }}</div>
                  </div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 15">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="t">{{ item.reason }} {{ item.playerName }}</div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 16">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="t">{{ item.reason }} {{ item.playerName }}</div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 17">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div class="t">{{ item.reason }} {{ item.playerName }}</div>
                  </div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 28">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="sub-info">
                    <!-- <div v-if="item.varResult <= 4">{{ item.homeScore }}-{{ item.awayScore }}</div> -->
                    <div>{{ item.playerName }}</div>
                    <div>{{ varType[item.varResult]?.text || '未知' }}</div>
                  </div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 29">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div>{{ item.playerName }}</div>
                  </div>
                </div>
              </div>
              <div class="event-item" v-if="item.incidentType === 30">
                <div class="event-sub-item">
                  <details-icons class="icon" :typeId="item.incidentType"></details-icons>
                  <div class="sub-info">
                    <div>{{ item.homeScore }}-{{ item.awayScore }}</div>
                    <div>{{ item.playerName }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="time" v-if="item.incidentType === 29 || item.incidentType === 30">点</div>
            <div class="time" v-else>{{ item.time }}'</div>
          </div>
          <div class="import-ant" v-if="item.position === 0">
            <div class="content">{{ item.textName }}</div>
          </div>
        </div>
      </div>
    </template>
    <div class="none-box" v-else>
      <NoneBox text="暂无事件" />
    </div>
    <!-- <div class="status-title">比赛结束</div> -->
  </div>
</template>
<!-- 
1 进球
2 角球
3 黄牌
4 红牌
5 越位
6 任意球
7 球门球
8 点球
9 换人
10 比赛开始
11 中场
12 结束
13 半场比分
15 两黄变红
16 点球未进
17 乌龙球
18 助攻
19 伤停补时
21 射正
22 射偏
23 进攻
24 危险进攻
25 控球率
26 加时赛结束
27 点球大战结束
28 VAR(视频助理裁判)
29 点球(点球大战)(type_v2字段返回)
30 点球未进(点球大战)(type_v2字段返回) -->
<!-- 1-进球有效
2-进球无效
3-点球有效
4-点球取消
5-红牌有效
6-红牌取消
7-出牌处罚核实
8-出牌处罚更改
9-维持原判
10-判罚更改
0-未知 -->
<script setup>
import { ref, computed } from 'vue';
import DetailsIcons from '@/components/module/data-icon/index.vue';
import NoneBox from '@/components/module/none-box/index.vue';
const props = defineProps({
  incidents: {
    type: Array,
    default: [
      {
        id: 0,
      }
    ]
  },
})
const varType = {
  1: {
    text: '进球有效'
  },
  2: {
    text: '进球无效'
  },
  3: {
    text: '点球取消'
  },
  4: {
    text: '点球取消'
  },
  5: {
    text: '红牌有效'
  },
  6: {
    text: '红牌取消'
  },
  7: {
    text: '出牌处罚核实'
  },
  8: {
    text: '出牌处罚更改'
  },
  9: {
    text: '维持原判'
  },
  10: {
    text: '判罚更改'
  },
  11: {
    text: '判罚点球'
  },
  12: {
    text: '不判罚点球'
  },
  0: {
    text: '未知'
  }
}
let isGoal = ref(false)
const filteredIncidents = computed(() => {
  let filterList = [];
  if (isGoal.value) {
    filterList = props.incidents.filter(item => {
      return item.incidentType === 1 || item.position === 0 || item.incidentType === 17 || item.incidentType === 8 || item.incidentType === 29;
    }); // 1为进球类型
    if (filterList.length > 0) {
      return filterList
    } else {
      return props.incidents;
    }
  }
  return props.incidents;
});
</script>

<style lang="scss" scoped>
.incidents-box {
  position: relative;
  padding: 40px 0px;
  background-color: #fff;

  .just-goal {
    display: flex;
    position: absolute;
    right: 10px;
    font-size: 12px;
    color: #818181;
    align-items: center;

    .t {
      margin-right: 4px;
    }

    .checkbox-group {
      margin-top: 2px;
    }
  }

  .sub-box {
    position: relative;
    padding: 20px 0 0 0;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 1px;
      background-color: #e0e0e0;
      z-index: 1;
    }
  }

  .status-title {
    width: fit-content;
    padding: 4px 24px;
    border-radius: 16px;
    font-size: 16px;
    color: #333;
    border: 1px solid #FB2B1F;
    margin-left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
  }

  .ind-item {
    margin: 34px 0;
    width: 100%;
    position: relative;

    .import-ant {
      position: relative;
      display: flex;
      justify-content: center;

      .content {
        // position: absolute;
        z-index: 9;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #FB2B1F;
        background-color: #fff;
        color: #333;
        width: fit-content;
        padding: 4px 20px;
        border-radius: 20px;
        font-size: 14px;
        // right: 50%;
        // top: 50%;
        // transform: translate(50%, -50%);
      }

    }

    .ind-sub-item {
      width: 100%;
      display: flex;
      position: relative;

      &.home-container {
        justify-content: flex-start;
      }

      &.away-container {
        justify-content: flex-end;
      }

      .time {
        position: absolute;
        background-color: #fff;
        z-index: 9;
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 40px;
        border: 1px solid #ccc;
        padding: 4px;
        border-radius: 50%;
        font-size: 14px;
        line-height: 1;
        right: 50%;
        top: 50%;
        transform: translate(50%, -50%);
      }

      .home-item,
      .away-item {
        width: 50%;
        color: #333;
        position: relative;
        box-sizing: border-box;
        font-size: 14px;

        .event-item {
          width: fit-content;
          background: #F4F4F4;
          padding: 8px;
          box-sizing: border-box;
          border-radius: 4px;
        }
      }

      .home-item {
        padding-right: 40px;
        display: flex;
        justify-content: flex-end;

        .event-sub-item {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-bottom: 2px;

          &:last-child {
            margin-bottom: 0;
          }

          .t {
            margin-right: 2px;
          }

          .sub-info {
            text-align: right;
            padding-right: 4px;
          }
        }
      }

      .away-item {
        padding-left: 40px;

        .event-sub-item {
          display: flex;
          align-items: center;

          .t {
            margin-left: 4px;
          }

          .sub-info {
            text-align: left;
            padding-left: 4px;
          }
        }
      }
    }
  }
}
</style>