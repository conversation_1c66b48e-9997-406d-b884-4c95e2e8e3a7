import { onMounted, onBeforeUnmount, type Ref } from 'vue'

export interface UseClickOutsideOptions {
  ignore?: string[] // 忽略的选择器
  capture?: boolean // 是否在捕获阶段处理
  delay?: number   // 关闭延迟
}

export function useClickOutside(
  elementRef: Ref<HTMLElement | null>,
  callback: () => void,
  options: UseClickOutsideOptions = {}
) {
  const { ignore = [], capture = false, delay = 0 } = options

  const handler = (e: MouseEvent) => {
    const target = e.target as HTMLElement

    // 检查是否点击了需要忽略的元素
    if (ignore.some(selector => target.closest(selector))) {
      return
    }

    if (elementRef.value && !elementRef.value.contains(target)) {
      if (delay > 0) {
        setTimeout(callback, delay)
      } else {
        callback()
      }
    }
  }

  onMounted(() => {
    document.addEventListener('click', handler, capture)
  })

  onBeforeUnmount(() => {
    document.removeEventListener('click', handler, capture)
  })
}