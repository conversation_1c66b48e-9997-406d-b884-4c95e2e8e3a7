import dayjs, { Dayjs } from 'dayjs'
import isToday from 'dayjs/plugin/isToday'
import isYesterday from 'dayjs/plugin/isYesterday'
import isTomorrow from 'dayjs/plugin/isTomorrow'

// 扩展 dayjs 插件
dayjs.extend(isToday)
dayjs.extend(isYesterday)
dayjs.extend(isTomorrow)

type Timestamp = number | string | Date

/**
 * 根据时间戳判断日期状态
 * @param {Timestamp} timestamp - 时间戳或可转换为日期的时间值
 * @returns {string} - 返回日期状态字符串
 *   '今天' | '昨天' | '明天' | 'MM-DD'
 */
export const formatDate = (timestamp: Timestamp): string => {
  const date: Dayjs = dayjs(timestamp)

  if (date.isToday()) {
    return `${date.format('MM-DD')} 今天`
  }

  if (date.isYesterday()) {
    return `${date.format('MM-DD')} 昨天`
  }

  if (date.isTomorrow()) {
    return `${date.format('MM-DD')} 明天`
  }

  // 如果不是今天、昨天或明天，返回格式化的日期
  return date.format('MM-DD')
}