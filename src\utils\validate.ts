import type { FormItemRule } from "naive-ui"

// 手机号校验规则
export const validatePhone = (rule: FormItemRule, value: string) => {
  if (!value) {
    return new Error('请输入手机号码')
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    return new Error('请输入正确的手机号码')
  }
  return true
}

// 验证码校验规则
export const validateVerifyCode = (rule: FormItemRule, value: string) => {
  if (!value) {
    return new Error('请输入验证码')
  }
  if (value.length !== 4) {
    return new Error('验证码长度应为4位')
  }
  return true
}

// 密码校验规则
export const validatePassword = (rule: FormItemRule, value: string) => {
  if (!value) {
    return new Error('请输入登录密码')
  }
  if (value.length < 6 || value.length > 16) {
    return new Error('密码长度应为6-16位')
  }
  // 检查是否包含字母
  const hasLetter = /[a-zA-Z]/.test(value)
  // 检查是否包含数字
  const hasNumber = /[0-9]/.test(value)

  if (!hasLetter || !hasNumber) {
    return new Error('密码必须包含英文和数字')
  }

  // 检查是否只包含英文和数字
  // if (!/^[a-zA-Z0-9]+$/.test(value)) {
  //   return new Error('密码只能包含英文和数字')
  // }
  return true
}

// 用户名校验规则
export const validateUserName = (rule: FormItemRule, value: string) => {
  if (!value) {
    return new Error('请输入用户名')
  }
  if (value.length < 2 || value.length > 18) {
    return new Error('用户名长度应为2-18个字符')
  }
  return true
}

// 协议校验规则
export const validateAgreement = (rule: FormItemRule, value: boolean) => {
  if (!value) {
    return new Error('请勾选用户协议')
  }
  return true
}