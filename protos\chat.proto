syntax = "proto3";

package chat;
import "common.proto";

option java_package = "com.letu.solutions.model.netty.v2.protobuf.chat";
option java_outer_classname = "Chat";

// 聊天消息类型
enum ChatMessageType {
  TEXT = 0;    //文本
  AUDIO = 1;   //音频
  IMAGE = 2;   //图片
  VIDEO = 3;   //视频
  FILE = 4;    //文件
  GIFT = 5;    //礼物
  ENTER = 6;   //进入直播间提示
  FOCUS = 7;   //关注主播
}

enum ReceiverType{
  SINGLE = 0;      //单聊
  GROUP = 1;       //群组
  CHAT_ROOM = 2;   //聊天室
  SUPER_GROUP = 3; //超大群
  LIVE_ROOM = 4;   //直播间
}

// 聊天消息
message ChatMessage {
  ChatMessageType chat_message_type = 1;   //消息类型
  string sender_id = 2;         //发送者
  string receiver_id = 3;       //接收者-可以是房间
  string content = 4;           //内容
  int64 sequence = 5;
  ReceiverType receiver_type = 6;  //消息类型
  string extra = 7;
  common.User user = 8;
}

// 附件（可扩展）
message Attachment {
  string url = 1;
}

// 消息回执
message MessageAck {
  string message_id = 1;
  bool success = 2;
  int64 sequence = 3;
}

message HistoryMessages {
  repeated ChatMessage histories = 1;
}

