import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      imports: ['vue'],
      resolvers: [NaiveUiResolver()]
    }),
    Components({
      resolvers: [NaiveUiResolver()]
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  css: {
    preprocessorOptions: {
      //define global scss variable
      scss: {
        additionalData: `@use "@/assets/index.scss" as *;`
      }
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,      // 端口号
    open: true,      // 自动打开浏览器
    cors: true,      // 允许跨域
    // proxy: {
    //   // 代理视频流请求
    //   '/api/video': {
    //     target: 'http://**************',
    //     changeOrigin: true,
    //     rewrite: (path) => path.replace(/^\/api\/video/, '')
    //   }
    // }
  }
})
