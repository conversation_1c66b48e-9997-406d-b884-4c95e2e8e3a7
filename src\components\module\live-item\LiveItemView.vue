<template>
  <div class="live-item-view">
    <div class="page-header">
      <div class="line"></div>
      <h3>直播列表</h3>
    </div>
    <div class="page-content">
      <div class="live-grid">
        <LiveItem v-for="live in liveList" :key="live.id" :live-data="live" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LiveItem from './LiveItem.vue'

interface Streamer {
  name: string
  avatar: string
}

interface LiveData {
  id: number
  matchId: number
  liveTitle: string
  liveCover: string
  userName: string
  userImage: string
  hotMax: number
  userId: number
  liveType: string
  url?: string
  sclassName: string
  homeName: string
  awayName: string
}

// 示例数据
const liveList = ref<LiveData[]>([])

const handleLiveClick = (liveData: LiveData) => {
  console.log('点击直播项:', liveData)
  // 这里可以处理直播项点击事件，比如跳转到直播页面
}
</script>

<style lang="scss" scoped>
.live-item-view {
  background-color: #fff;
  min-height: 100%;

  .page-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .page-content {
    padding: 0 36px 40px;

    .live-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
  }
}
</style>
