import CryptoJS from 'crypto-js'
import { useUserStore } from '@/stores/user'

export function signRequest(config: any) {
  const userStore = useUserStore()
  // const path = config.url;
  const timestamps = Date.now().toString()
  const key = 'shDOUArrDhpeAMw9FGY79Zmy3MLWwNWy'

  // 添加 timestamps 到 headers
  config.headers['timestamps'] = timestamps

  // const test = loadParam(path, config.data);
  const test = loadParam(config)
  const token = userStore.userToken
  // 添加token
  config.headers['token'] = token || ''

  config.headers['os'] = 'web'

  config.headers['businessType'] = 'live_customer'
  config.headers['deviceId'] = 'systemInfo.deviceId'
  config.headers['codeChannel'] = 'live_web';

  // AES 加密 timestamps
  const aesTimestamp = CryptoJS.AES.encrypt(
    timestamps,
    CryptoJS.enc.Utf8.parse('4d5bc50346c22dde12be2c3b1b89ada6'),
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.ZeroPadding
    }
  )
  config.headers['envTimestamps'] = aesTimestamp.toString()
  // 计算签名
  config.headers['sign'] = CryptoJS.MD5(test + timestamps + key)
    .toString(CryptoJS.enc.Hex)
    .toLowerCase()
  return config
}

function loadParam(config: any): string {
  let str = ''
  if (!(config.headers['Content-Type'] && config.headers['Content-Type'].includes('multipart/form-data'))) {
    if (config.data || config.params) {
      if (config.method === 'get') {
        if (config.params) {
          const params = config.params
          const keyList = Object.keys(config.params)
          // 解析 GET 请求的查询参数
          // eslint-disable-next-line max-depth
          for (const key of keyList) {
            str +=
              keyList[keyList.length - 1] !== key
                ? `${key}=${encodeURI(<string>params[key])}&`
                : `${key}=${encodeURI(<string>params[key])}`
          }
        }
      } else if (config.data) {
        str = JSON.stringify(config.data)
      }
    }
  } else {
    const url = config.url
    if (url.includes('?')) {
      str += url.substring(url.indexOf('?') + 1, url.length)
    }
  }

  return str
}
