<template>
  <div class="user-messages">
    <div class="page-header">
      <h3>我的私信</h3>
    </div>
    <div class="page-content">
      <p>私信功能开发中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style lang="scss" scoped>
.user-messages {
  .page-header {
    margin-bottom: 30px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
  
  .page-content {
    background: #ffffff;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    color: #666;
  }
}
</style>
