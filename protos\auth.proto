syntax = "proto3";

package auth;

import "common.proto";

option java_package = "com.letu.solutions.model.netty.v2.protobuf.auth";
option java_outer_classname = "Auth";

// 登录请求
message LoginRequest {
  string token = 1;     //token
  common.DevicePlatform device_platform = 2;  //设备
}

// 登录响应
message LoginResponse {
  bool success = 1;   //登录是否成功
  string error_message = 3;  //错误信息
  common.User user = 4;  //返回用户信息
}

//注销请求
message LogoutRequest {
  common.DevicePlatform device_platform = 1;
}

//注销响应
message LogoutResponse {
  string logout_reason = 1;
}
