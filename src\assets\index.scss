// Do not edit directly
:root,
html[data-theme='light'] {
  --index:rgb(235, 0, 0);
  --color-transparent-index-900: rgba(235, 0, 0, var(--opacity-80));
  --color-transparent-index-800: rgba(235, 0, 0, var(--opacity-60));
  --color-transparent-index-700: rgba(235, 0, 0, var(--opacity-45));
  --color-transparent-index-600: rgba(235, 0, 0, var(--opacity-35));
  --color-transparent-index-500: rgba(235, 0, 0, var(--opacity-20));
  --color-transparent-index-400: rgba(235, 0, 0, var(--opacity-12));
  --color-transparent-index-300: rgba(235, 0, 0, var(--opacity-8));
  --color-transparent-index-200: rgba(235, 0, 0, var(--opacity-4));
  --color-transparent-index-100: rgba(235, 0, 0, var(--opacity-2));

  --duration-1000: 0.5s;
  --duration-900: 0.45s;
  --duration-800: 0.4s;
  --duration-700: 0.35s;
  --duration-600: 0.3s;
  --duration-500: 0.25s;
  --duration-400: 0.22s;
  --duration-300: 0.19s;
  --duration-200: 0.16s;
  --duration-100: 0.13s;
  --font-weight-700: 700;
  --font-weight-600: 600;
  --font-weight-500: 500;
  --font-weight-400: 400;
  --font-weight-300: 300;
  --color-static-black: #000000;
  --color-static-white: #ffffff;
  --color-violet-900: #4c0973;
  --color-violet-800: #681094;
  --color-violet-700: #911cc4;
  --color-violet-600: #cf33ff;
  --color-violet-500: #d858ff;
  --color-violet-400: #e17eff;
  --color-violet-300: #eaa7ff;
  --color-violet-200: #f3ceff;
  --color-violet-100: #faebff;
  --color-green-900: #074425;
  --color-green-800: #0b6636;
  --color-green-700: #16aa56;
  --color-green-600: #1ccc66;
  --color-green-500: #45d582;
  --color-green-400: #70df9f;
  --color-green-300: #9de9bd;
  --color-green-200: #c9f3da;
  --color-green-100: #e8faf0;
  --color-orange-900: #61240a;
  --color-orange-800: #7a330d;
  --color-orange-700: #cb6a1a;
  --color-orange-600: #f48b22;
  --color-orange-500: #f6a04a;
  --color-orange-400: #f8b674;
  --color-orange-300: #facda0;
  --color-orange-200: #fce3ca;
  --color-orange-100: #fef3e9;
  --color-blue-900: #001182;
  --color-blue-800: #0722a8;
  --color-blue-700: #1338cf;
  --color-blue-600: #2254f4;
  --color-blue-500: #4d7cff;
  --color-blue-400: #759fff;
  --color-blue-300: #9ebeff;
  --color-blue-200: #c7dbff;
  --color-blue-100: #f0f6ff;
  --color-cyan-900: #09384f;
  --color-cyan-800: #0f5170;
  --color-cyan-700: #1778a1;
  --color-cyan-600: #24aee0;
  --color-cyan-500: #4cc0e6;
  --color-cyan-400: #75d1ec;
  --color-cyan-300: #a1e1f3;
  --color-cyan-200: #cff0f9;
  --color-cyan-100: #ecf9fc;
  --color-yellow-900: #594000;
  --color-yellow-800: #8a6800;
  --color-yellow-700: #d6ac00;
  --color-yellow-600: #f2c200;
  --color-yellow-500: #f4cd2e;
  --color-yellow-400: #f7d95e;
  --color-yellow-300: #f9e591;
  --color-yellow-200: #fcf0c2;
  --color-yellow-100: #fef9e6;
  --color-red-900: #732017;
  --color-red-800: #a81611;
  --color-red-700: #cf2b1f;
  --color-red-600: #f54531;
  --color-red-500: #ff725c;
  --color-red-400: #ff9985;
  --color-red-300: #ffbead;
  --color-red-200: #ffe0d6;
  --color-red-100: #fff4f0;
  --color-pink-900: #700c34;
  --color-pink-800: #941543;
  --color-pink-700: #c42158;
  --color-pink-600: #ff3b6f;
  --color-pink-500: #ff5e89;
  --color-pink-400: #ff84a4;
  --color-pink-300: #ffabc1;
  --color-pink-200: #ffd0dc;
  --color-pink-100: #ffebf1;
  --color-teal-900: #07443a;
  --color-teal-800: #0b6655;
  --color-teal-700: #16aa8a;
  --color-teal-600: #1ccca3;
  --color-teal-500: #42d6b1;
  --color-teal-400: #70dfc5;
  --color-teal-300: #9aebd4;
  --color-teal-200: #cbf5e8;
  --color-teal-100: #e8faf6;
  --color-purple-900: #281059;
  --color-purple-800: #3a1a7d;
  --color-purple-700: #512aad;
  --color-purple-600: #7146e8;
  --color-purple-500: #8b67ec;
  --color-purple-400: #a68af1;
  --color-purple-300: #c2aff5;
  --color-purple-200: #ddd3f9;
  --color-purple-100: #f1edfd;
  --color-gradient-lightgray: #000000;
  --color-gradient-white: #000000;
  --color-gradient-gray: #000000;
  --color-gradient-brand: #000000;
  --color-gradient-orange: #000000;
  --color-gradient-yellow: #000000;
  --color-gradient-pink: #000000;
  --color-gradient-violet: #000000;
  --color-gradient-purple: #000000;
  --color-gradient-blue: #000000;
  --color-gradient-cyan: #000000;
  --color-gradient-green: #000000;
  --color-gray-1000: #000000;
  --color-gray-900: #222529;
  --color-gray-800: #4c535c;
  --color-gray-700: #7f8792;
  --color-gray-600: #9da3ac;
  --color-gray-500: #b4b8bf;
  --color-gray-400: #d9dcdf;
  --color-gray-300: #e8eaec;
  --color-gray-200: #f1f2f4;
  --color-gray-100: #f6f7f9;
  --color-gray-0: #ffffff;
  --opacity-100: 1;
  --opacity-90: 0.9;
  --opacity-80: 0.8;
  --opacity-64: 0.64;
  --opacity-60: 0.6;
  --opacity-56: 0.56;
  --opacity-48: 0.48;
  --opacity-45: 0.45;
  --opacity-40: 0.4;
  --opacity-35: 0.35;
  --opacity-30: 0.3;
  --opacity-24: 0.24;
  --opacity-20: 0.2;
  --opacity-16: 0.16;
  --opacity-12: 0.12;
  --opacity-8: 0.08;
  --opacity-4: 0.04;
  --opacity-2: 0.02;
  --opacity-0: 0;
  --font-family-number: 'JetBrains Mono', 'Helvetica Neue', Helvetica, 'PingFang SC',
    'Microsoft YaHei', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', Arial, sans-serif,
    Apple Color Emoji, Segoe UI Emoji;
  --font-family-body: 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', 'WenQuanYi Micro Hei',
    Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji;
  --line-height-xlarge: 1.9;
  --line-height-large: 1.7;
  --line-height-medium: 1.5;
  --line-height-small: 1.3;
  --line-height-xsmall: 1.1;
  --font-size-base: 4px;
  --font-size-900: 46px;
  --font-size-800: 42px;
  --font-size-750: 40px;
  --font-size-700: 38px;
  --font-size-650: 36px;
  --font-size-600: 34px;
  --font-size-550: 32px;
  --font-size-500: 30px;
  --font-size-450: 28px;
  --font-size-400: 26px;
  --font-size-350: 24px;
  --font-size-300: 22px;
  --font-size-250: 20px;
  --font-size-200: 18px;
  --font-size-150: 16px;
  --font-size-125: 15px;
  --font-size-100: 14px;
  --font-size-75: 13px;
  --font-size-50: 12px;
  --font-size-25: 11px;
  --font-size-20: 10px;
  --spacing-1000: 40px;
  --spacing-950: 38px;
  --spacing-900: 36px;
  --spacing-850: 34px;
  --spacing-800: 32px;
  --spacing-750: 30px;
  --spacing-700: 28px;
  --spacing-650: 26px;
  --spacing-600: 24px;
  --spacing-550: 22px;
  --spacing-500: 20px;
  --spacing-450: 18px;
  --spacing-400: 16px;
  --spacing-350: 14px;
  --spacing-300: 12px;
  --spacing-250: 10px;
  --spacing-225: 9px;
  --spacing-200: 8px;
  --spacing-150: 6px;
  --spacing-125: 5px;
  --spacing-100: 4px;
  --spacing-75: 3px;
  --spacing-50: 2px;
  --spacing-25: 1px;
  --spacing-0: 0px;
  --size-1200: 48px;
  --size-1000: 40px;
  --size-950: 38px;
  --size-900: 36px;
  --size-850: 34px;
  --size-800: 32px;
  --size-750: 30px;
  --size-700: 28px;
  --size-650: 26px;
  --size-600: 24px;
  --size-550: 22px;
  --size-500: 20px;
  --size-450: 18px;
  --size-400: 16px;
  --size-350: 14px;
  --size-300: 12px;
  --size-250: 10px;
  --size-200: 8px;
  --size-150: 6px;
  --size-125: 5px;
  --size-100: 4px;
  --size-75: 3px;
  --size-50: 2px;
  --size-25: 1px;
  --size-0: 0px;
  --version: 0.2-beta.3;
  --color-transparent-regular: rgba(0, 0, 0, 0);
  --color-transparent-green-900: rgba(36, 179, 95, var(--opacity-80));
  --color-transparent-green-800: rgba(36, 179, 95, var(--opacity-64));
  --color-transparent-green-700: rgba(36, 179, 95, var(--opacity-56));
  --color-transparent-green-600: rgba(36, 179, 95, var(--opacity-48));
  --color-transparent-green-500: rgba(36, 179, 95, var(--opacity-40));
  --color-transparent-green-400: rgba(36, 179, 95, var(--opacity-30));
  --color-transparent-green-300: rgba(36, 179, 95, var(--opacity-24));
  --color-transparent-green-200: rgba(36, 179, 95, var(--opacity-16));
  --color-transparent-green-100: rgba(36, 179, 95, var(--opacity-8));
  --color-transparent-orange-900: rgba(250, 170, 50, var(--opacity-80));
  --color-transparent-orange-800: rgba(250, 170, 50, var(--opacity-64));
  --color-transparent-orange-700: rgba(250, 170, 50, var(--opacity-56));
  --color-transparent-orange-600: rgba(250, 170, 50, var(--opacity-48));
  --color-transparent-orange-500: rgba(250, 170, 50, var(--opacity-40));
  --color-transparent-orange-400: rgba(250, 170, 50, var(--opacity-30));
  --color-transparent-orange-300: rgba(250, 170, 50, var(--opacity-24));
  --color-transparent-orange-200: rgba(250, 170, 50, var(--opacity-16));
  --color-transparent-orange-100: rgba(250, 170, 50, var(--opacity-8));
  --color-transparent-red-900: rgba(245, 69, 49, var(--opacity-80));
  --color-transparent-red-800: rgba(245, 69, 49, var(--opacity-64));
  --color-transparent-red-700: rgba(245, 69, 49, var(--opacity-56));
  --color-transparent-red-600: rgba(245, 69, 49, var(--opacity-48));
  --color-transparent-red-500: rgba(245, 69, 49, var(--opacity-40));
  --color-transparent-red-400: rgba(245, 69, 49, var(--opacity-30));
  --color-transparent-red-300: rgba(245, 69, 49, var(--opacity-24));
  --color-transparent-red-200: rgba(245, 69, 49, var(--opacity-16));
  --color-transparent-red-100: rgba(245, 69, 49, var(--opacity-8));
  --color-transparent-blue-900: rgba(34, 84, 244, var(--opacity-80));
  --color-transparent-blue-800: rgba(34, 84, 244, var(--opacity-64));
  --color-transparent-blue-700: rgba(34, 84, 244, var(--opacity-56));
  --color-transparent-blue-600: rgba(34, 84, 244, var(--opacity-48));
  --color-transparent-blue-500: rgba(34, 84, 244, var(--opacity-40));
  --color-transparent-blue-400: rgba(34, 84, 244, var(--opacity-30));
  --color-transparent-blue-300: rgba(34, 84, 244, var(--opacity-24));
  --color-transparent-blue-200: rgba(34, 84, 244, var(--opacity-16));
  --color-transparent-blue-100: rgba(34, 84, 244, var(--opacity-8));
  --color-transparent-black-900: rgba(0, 0, 0, var(--opacity-80));
  --color-transparent-black-800: rgba(0, 0, 0, var(--opacity-60));
  --color-transparent-black-700: rgba(0, 0, 0, var(--opacity-45));
  --color-transparent-black-600: rgba(0, 0, 0, var(--opacity-35));
  --color-transparent-black-500: rgba(0, 0, 0, var(--opacity-20));
  --color-transparent-black-400: rgba(0, 0, 0, var(--opacity-12));
  --color-transparent-black-300: rgba(0, 0, 0, var(--opacity-8));
  --color-transparent-black-200: rgba(0, 0, 0, var(--opacity-4));
  --color-transparent-black-100: rgba(0, 0, 0, var(--opacity-2));
  --color-transparent-white-900: rgba(255, 255, 255, var(--opacity-80));
  --color-transparent-white-800: rgba(255, 255, 255, var(--opacity-60));
  --color-transparent-white-700: rgba(255, 255, 255, var(--opacity-45));
  --color-transparent-white-600: rgba(255, 255, 255, var(--opacity-35));
  --color-transparent-white-500: rgba(255, 255, 255, var(--opacity-20));
  --color-transparent-white-400: rgba(255, 255, 255, var(--opacity-12));
  --color-transparent-white-300: rgba(255, 255, 255, var(--opacity-8));
  --color-transparent-white-200: rgba(255, 255, 255, var(--opacity-4));
  --color-transparent-white-100: rgba(255, 255, 255, var(--opacity-2));

  --font-weight-bold: 600;
  --font-weight-medium-bold: 500;
  --font-weight-regular: 400;

  --boxshadow-large: 0px 2px 8px 0px rgba(0, 0, 0, 0.04), 0px 12px 48px 2px rgba(0, 0, 0, 0.08);
  --boxshadow-medium: 0px 0px 1px 0px rgba(0, 0, 0, 0.2), 0px 8px 32px 2px rgba(0, 0, 0, 0.08);
  --boxshadow-small: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 4px 12px 0px rgba(0, 0, 0, 0.04);

  --number-h1-bold: var(--font-weight-700) var(--font-size-750) / var(--line-height-small)
  var(--font-family-number);
--number-h1-regular: var(--font-weight-regular) var(--font-size-750) / var(--line-height-small)
  var(--font-family-number);
--number-h2-bold: var(--font-weight-700) var(--font-size-550) / var(--line-height-small)
  var(--font-family-number);
--number-h2-regular: var(--font-weight-regular) var(--font-size-550) / var(--line-height-small)
  var(--font-family-number);
--number-h3-bold: var(--font-weight-700) var(--font-size-450) / var(--line-height-small)
  var(--font-family-number);
--number-h3-regular: var(--font-weight-regular) var(--font-size-450) / var(--line-height-small)
  var(--font-family-number);
--number-h4-bold: var(--font-weight-700) var(--font-size-350) / var(--line-height-small)
  var(--font-family-number);
--number-h4-regular: var(--font-weight-regular) var(--font-size-350) / var(--line-height-small)
  var(--font-family-number);
--number-h5-bold: var(--font-weight-700) var(--font-size-250) / var(--line-height-small)
  var(--font-family-number);
--number-h5-regular: var(--font-weight-regular) var(--font-size-250) / var(--line-height-small)
  var(--font-family-number);
--number-h6-bold: var(--font-weight-700) var(--font-size-150) / var(--line-height-medium)
  var(--font-family-number);
--number-h6-regular: var(--font-weight-regular) var(--font-size-150) / var(--line-height-medium)
  var(--font-family-number);
--number-p2-bold: var(--font-weight-700) var(--font-size-50) / var(--line-height-medium)
  var(--font-family-number);
--number-p2-regular: var(--font-weight-regular) var(--font-size-50) / var(--line-height-medium)
  var(--font-family-number);
--number-p1-bold: var(--font-weight-700) var(--font-size-100) / var(--line-height-medium)
  var(--font-family-number);
--number-p1-regular: var(--font-weight-regular) var(--font-size-100) / var(--line-height-medium)
  var(--font-family-number);
--text-p2-bold: var(--font-weight-bold) var(--font-size-50) / var(--line-height-medium)
  var(--font-family-body);
--text-p2-regular: var(--font-weight-regular) var(--font-size-50) / var(--line-height-medium)
  var(--font-family-body);
--text-p1-bold: var(--font-weight-bold) var(--font-size-100) / var(--line-height-medium)
  var(--font-family-body);
--text-p1-regular: var(--font-weight-regular) var(--font-size-100) / var(--line-height-medium)
  var(--font-family-body);
--text-h6-bold: var(--font-weight-bold) var(--font-size-150) / var(--line-height-small)
  var(--font-family-body);
--text-h6-regular: var(--font-weight-regular) var(--font-size-150) / var(--line-height-small)
  var(--font-family-body);
--text-h5-bold: var(--font-weight-bold) var(--font-size-250) / var(--line-height-small)
  var(--font-family-body);
--text-h5-regular: var(--font-weight-regular) var(--font-size-250) / var(--line-height-small)
  var(--font-family-body);
--text-h4-bold: var(--font-weight-bold) var(--font-size-350) / var(--line-height-small)
  var(--font-family-body);
--text-h4-regular: var(--font-weight-regular) var(--font-size-350) / var(--line-height-small)
  var(--font-family-body);
--text-h3-bold: var(--font-weight-bold) var(--font-size-450) / var(--line-height-small)
  var(--font-family-body);
--text-h3-regular: var(--font-weight-regular) var(--font-size-450) / var(--line-height-small)
  var(--font-family-body);
--text-h2-bold: var(--font-weight-bold) var(--font-size-650) / var(--line-height-small)
  var(--font-family-body);
--text-h2-regular: var(--font-weight-regular) var(--font-size-650) / var(--line-height-small)
  var(--font-family-body);
--text-h1-bold: var(--font-weight-bold) var(--font-size-750) / var(--line-height-small)
  var(--font-family-body);
--text-h1-regular: var(--font-weight-regular) var(--font-size-750) / var(--line-height-small)
  var(--font-family-body);
}

/* semantic color variables for this project */
:root {
  
}
