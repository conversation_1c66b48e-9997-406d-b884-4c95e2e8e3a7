<template>
  <div class="gift-rank-box">
    <div class="top-box">
      <div class="rank-top" v-if="topList[1]">
        <div class="logo">
          <defaultLogo type="player" :logo="topList[1]?.userImage" width="60px" height="60px"></defaultLogo>
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">{{ topList[1]?.userName }}<img class="sex" v-if="topList[1]?.sex === 'boy'"
            src="@/static/live/man-icon.png" />
          <img class="sex" v-if="topList[1]?.sex === 'girl'" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img v-if="type === 'rq'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'qp'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'gx'" src="@/static/live/<EMAIL>" />{{ topList[1]?.num }}
        </div>
      </div>
      <div class="rank-top" v-if="topList[0]">
        <div class="logo">
          <defaultLogo type="player" :logo="topList[0]?.userImage" width="60px" height="60px"></defaultLogo>
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">{{ topList[0]?.userName }}<img class="sex" v-if="topList[0]?.sex === 'boy'"
            src="@/static/live/man-icon.png" />
          <img class="sex" v-if="topList[0]?.sex === 'girl'" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img v-if="type === 'rq'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'qp'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'gx'" src="@/static/live/<EMAIL>" />{{ topList[0]?.num }}
        </div>
      </div>
      <div class="rank-top" v-if="topList[2]">
        <div class="logo">
          <defaultLogo type="player" :logo="topList[2]?.userImage" width="60px" height="60px"></defaultLogo>
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">{{ topList[2]?.userName }}<img class="sex" v-if="topList[2]?.sex === 'boy'"
            src="@/static/live/man-icon.png" />
          <img class="sex" v-if="topList[2]?.sex === 'girl'" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img v-if="type === 'rq'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'qp'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'gx'" src="@/static/live/<EMAIL>" />{{ topList[2]?.num }}
        </div>
      </div>
    </div>
    <div class="list-box">
      <div class="item" v-for="(item, index) in list">
        <span class="num">{{ index + 4 }}</span>
        <div class="info">
          <div class="logo">
            <defaultLogo type="player" :logo="item.userImage" width="60px" height="60px"></defaultLogo>
          </div>
          <div class="name">{{ item.userName }}</div>
          <img class="sex" v-if="item.sex === 'boy'" src="@/static/live/man-icon.png" />
          <img class="sex" v-if="item.sex === 'girl'" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img v-if="type === 'rq'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'qp'" src="@/static/live/<EMAIL>" />
          <img v-if="type === 'gx'" src="@/static/live/<EMAIL>" />{{ item.num }}
        </div>
      </div>
    </div>
    <noneBox v-if="publicList.length === 0" :imageSize="100" :textSize="14"></noneBox>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  publicList: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'rq'
  }
})
interface PublicListType {
  grade: number;
  userName: string;
  userImage: string;
  num: number;
  sex: string
}
let topList = ref<PublicListType[]>([])
let list = ref<PublicListType[]>([])
watch(() => props.publicList, (newData, oldData) => {
  // if (newData.length === 0) return
  topList.value = newData.slice(0, 3) as PublicListType[]
  list.value = newData.slice(3) as PublicListType[]
}, { immediate: true, deep: true })
</script>
<style scoped lang="scss">
.gift-rank-box {
  padding: 10px 3px 0 3px;
  box-sizing: border-box;
}

.top-box {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 2px;
  margin-bottom: 20px;

  .rank-top {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    padding: 10px 2px 10px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 102px;

    // 第二名 (左侧)
    &:nth-child(1) {
      // order: 1;
      // margin-top: 30px;

      // .badge {
      //   width: 24px;
      //   height: 24px;
      // }
      .logo {
        border-color: #CFE9F1;
      }
    }

    // 第一名 (中间，最高)
    &:nth-child(2) {
      height: 160px;
      // background: linear-gradient(135deg, #FFD700, #FFA500);
      margin-top: 0;

      .badge {
        width: 28px;
        height: 28px;
      }

      .logo {
        border-color: #F9E38B;
      }

      // .info {
      //   color: white;
      //   font-weight: bold;
      // }

      // .hot {
      //   color: white;
      // }
    }

    // 第三名 (右侧)
    &:nth-child(3) {
      //  : 3;
      // margin-top: 30px;

      // .badge {
      //   width: 24px;
      //   height: 24px;
      // }
      .logo {
        border-color: #F1AD70;
      }
    }

    .logo {
      position: relative;
      margin-bottom: 10px;
      border-radius: 50%;
      border: 2px solid;

      img:first-child {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .badge {
        position: absolute;
        bottom: -15px;
        right: -13px;
        transform: translateX(-50%);
        width: 44px;
        height: 27px;
      }
    }

    .info {
      gap: 5px;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-top: 10px;
      margin-bottom: 2px;
      max-width: 90px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;

      .sex {
        width: 16px;
        height: 16px;
      }
    }

    .hot {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #ff4757;
      font-weight: bold;

      img {
        width: 14px;
        height: 14px;
      }
    }
  }
}

.list-box {
  .item {
    display: flex;
    align-items: center;
    padding: 8px;
    background: white;
    border-bottom: 1px solid #D9D9D9;

    .num {
      font-size: 16px;
      font-weight: bold;
      color: #000;
      min-width: 30px;
      text-align: center;
      margin-right: 15px;
    }

    .info {
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;

      .logo {
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .name {
        font-size: 14px;
        font-weight: 400;
        color: #000;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        max-width: 120px;
      }

      .sex {
        width: 15px;
        height: 15px;
      }
    }

    .hot {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #FB2B1F;

      img {
        width: 10px;
        height: 12px;
      }
    }
  }
}
</style>