syntax = "proto3";

package live;

import "common.proto";

option java_package = "com.letu.solutions.model.netty.v2.protobuf.live";
option java_outer_classname = "Live";

// 登录请求
message EnterLiveRoomEvent {
    string roomId = 1;        //直播间id
    UserType user_type = 2;   //用户类型
}
enum UserType {
    ANONYMOUS = 0; //匿名
    SIGNED = 1;    //注册用户
}

// 退出直播间事件
message ExitLiveRoomEvent {
    bool success = 1;
    string error_message = 3;
    string roomId = 5;
}
