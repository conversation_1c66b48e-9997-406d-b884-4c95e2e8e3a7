<template>
  <div class="gift-rank-view">
    <div class="tabs">
      <div v-for="tab in tabs" :key="tab.value" class="tabs-item" :class="{ active: activeTab === tab.value }"
        @click="handleChangeTab(tab.value)">
        {{ tab.label }}
      </div>
    </div>
    <component :is="RankView" class="chap" :publicList="publicList" :type="activeTab" />
  </div>
</template>
<script setup lang="ts">
import RankView from './RankView.vue'
import { getPopularityApi, getTicketApi, getContributeApi } from '@/api/match'
const props = defineProps({
  liveId: {
    type: Number,
    default: 0
  }
})
interface PublicListType {
  grade: number;
  userName: string;
  userImage: string;
  num: number;
  sex: string
}
let publicList = ref<PublicListType[]>([])
let popularity = ref([])
const getPopularity = async () => {
  let res = await getPopularityApi()
  popularity.value = res.data
  publicList.value = res.data
}
let tickets = ref([])
const getTicket = async () => {
  let res = await getTicketApi()
  tickets.value = res.data;
}
let contributes = ref([])
const getContribute = async () => {
  let res = await getContributeApi({ liveId: props.liveId })
  contributes.value = res.data;
}
type Tab = {
  label: string
  value: string
}
const tabs: Tab[] = [
  { label: '人气排行榜', value: 'rq' },
  { label: '球票排行榜', value: 'qp' },
  { label: '贡献排行榜', value: 'gx' }
]
const activeTab = ref<string>('rq')
const handleChangeTab = (value: string) => {
  activeTab.value = value
  switch (value) {
    case 'rq':
      publicList.value = popularity.value
      break
    case 'qp':
      publicList.value = tickets.value
      break
    case 'gx':
      publicList.value = contributes.value
      break
    default:
      break
  }
}
onMounted(() => {
  //人气排行榜
  getPopularity()
  getTicket()
  getContribute()
})
</script>
<style scoped lang="scss">
.gift-rank-view {
  background: #F4F4F4;
  overflow: hidden;

  .tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    padding: 0 20px;
    margin: 8px 0;
    font-size: 16px;
    color: #333;
    cursor: pointer;

    .tabs-item {
      transition: all .3s ease;
      padding: 2px 5px;
      font-size: 14px;
      border-radius: 2px;

      &:hover:not(.active) {
        color: #FB2B1F;
      }
    }

    .active {
      background-color: #FB2B1F;
      color: #FFF;
    }
  }

  .chap {
    overflow-y: auto;
    height: calc(100% - 46px);
  }
}
</style>