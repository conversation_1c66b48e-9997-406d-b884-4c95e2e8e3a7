import * as WX from 'weixin-js-sdk'
//h5相关 jssdk
//config配置
export const weChatH5config = async function (res) {
  // let { appId, timestamp, nonceStr, signature } = res;
  WX.config({
    debug: false, // 开启调试模式,调用的所有 api 的返回值会在客户端 alert 出来，若要查看传入的参数，可以在 pc 端打开，参数信息会通过 log 打出，仅在 pc 端时才会打印。
    appId: res.appId, // 必填，公众号的唯一标识
    timestamp: res.timestamp, // 必填，生成签名的时间戳
    nonceStr: res.nonceStr, // 必填，生成签名的随机串
    signature: res.signature, // 必填，签名
    jsApiList: [
      'updateAppMessageShareData',
      'updateTimelineShareData',
      'chooseWXPay',
      'hideMenuItems',
      'getLocation',
      'openLocation',
      'hideAllNonBaseMenuItem',
      'showMenuItems',
      'chooseImage',
      'getLocalImgData'
    ] // 必填，需要使用的 JS 接口列表
  })
}
//h5支付
export const weChatH5Pay = function (data) {
  return new Promise((resove, reject) => {
    const WXPayData = {
      nonceStr: data.nonceStr,
      package: data.package,
      paySign: data.sign,
      signType: data.signType,
      timestamp: data.timeStamp
    }
    WX.chooseWXPay({
      ...WXPayData,
      success: function (wxPayRes) {
        resove(wxPayRes)
      },
      fail: function () {
        reject({
          msg: '支付失败'
        })
      },
      cancel: function () {
        reject({
          msg: '取消支付'
        })
      }
    })
  })
}
