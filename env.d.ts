/// <reference types="vite/client" />
import type { MessageApiInjection } from 'naive-ui/lib/message/src/MessageProvider'

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare module 'vue-wxlogin'
declare module 'vue-qr/src/packages/vue-qr.vue'
declare module 'weixin-js-sdk'
declare global {
  interface Window {
    $message: MessageApiInjection
  }
}

interface ImportMetaEnv {
  VITE_APP_TITLE: string
  VITE_APP_PORT: string
  VITE_APP_BASE_API: string
}
interface ImportMeta {
  readonly env: ImportMetaEnv
}
